'use client'

import showdown from "showdown";

const classMap = {
  h1: "typography-heading-4 font-weight-bold mb-2 whitespace-pre-wrap break-words",
  h2: "typography-body-lg font-weight-semibold mb-2 whitespace-pre-wrap break-words",
  h3: "typography-body font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h4: "text-medium font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h5: "typography-body-sm font-weight-medium mb-2  whitespace-pre-wrap break-words",
  h6: "typography-caption font-weight-medium mb-2  whitespace-pre-wrap break-words",
  p: "typography-body-sm font-weight-medium mb-1 whitespace-pre-wrap break-words",
  ul: "list-disc list-inside typography-body-sm font-weight-medium mb-2 whitespace-pre-wrap break-words",
  ol: "list-decimal ml-2 typography-body-sm font-weight-medium mb-2 whitespace-pre-wrap break-words",
  li: "ml-2 mt-2  whitespace-pre-wrap break-words  whitespace-pre-wrap break-words",
  strong: "font-weight-bold mt-2 text-gray-800 whitespace-pre-wrap break-words",
  a: "text-orange-600 hover:text-orange-800 underline  whitespace-pre-wrap break-words",
  blockquote: "border-l-4 border-gray-300 pl-4 py-1 mb-2 italic text-gray-600  whitespace-pre-wrap break-words",
  code: "markdown-code  bg-gray-100 rounded px-1 py-0.5 typography-body-sm text-gray-800 whitespace-pre-wrap break-words",
  pre: "markdown-pre bg-gray-100 rounded p-2 mb-2 whitespace-pre-wrap break-words",
  table: "min-w-full border-collapse mb-2  whitespace-pre-wrap break-words",
  th: "bg-gray-200 border border-gray-300 px-2 py-1 text-left text-gray-700  whitespace-pre-wrap break-words",
  td: "border border-gray-300 px-2 py-1 text-gray-600  whitespace-pre-wrap break-words",
  img: "max-w-full h-auto rounded shadow-lg mb-2  whitespace-pre-wrap break-words",
  hr: "border-t border-gray-300 my-2  whitespace-pre-wrap break-words",
  json: " typography-body-sm text-white m-1 border border-gray-300 p-2 rounded-md mb-2 bg-gray-800 whitespace-pre-wrap break-words",
};
const bindings = Object.keys(classMap).map((key) => ({
  type: 'output',
  regex: new RegExp(`<${key}(.*)>`, 'g'),
  replace: `<${key} class="${classMap[key]}" $1>`,
}));

const conv = new showdown.Converter({
  extensions: [...bindings],
  omitExtraWLInCodeBlocks: true,
  parseImgDimensions: true,
  simplifiedAutoLink: true,
  literalMidWordUnderscores: true,
  strikethrough: true,
  tables: true,
  ghCodeBlocks: true,
  tasklists: true,
  smoothLivePreview: true,
  ghCompatibleHeaderId: true,
  encodeEmails: true,
  ellipsis: true,
  emoji: true
});

export const renderHTML = (text) => {
  if(!text || typeof text !== "string") return null;
  
  // Preprocess text to improve markdown list formatting
  let processedText = text
    // Convert Unicode bullet points to markdown format
    .replace(/•\s+/g, '- ')
    .replace(/\u2022\s+/g, '- ')
    // Convert other common bullet characters
    .replace(/▪\s+/g, '- ')
    .replace(/▫\s+/g, '- ')
    .replace(/‣\s+/g, '- ')
    .replace(/⁃\s+/g, '- ')
    // Handle lines that start with bullet-like characters
    .replace(/^\s*[•▪▫‣⁃]\s+/gm, '- ')
    // Handle numbered lists - ensure proper spacing
    .replace(/([^\n])\n(\d+\.\s)/g, '$1\n\n$2')
    // Ensure proper line breaks before lists - but not double breaks which cause separation
    .replace(/([^\n])\n- /g, '$1\n\n- ')
    // Ensure proper spacing after dashes for list items
    .replace(/^- /gm, '- ')
    // Ensure proper spacing for numbered lists
    .replace(/^(\d+\.\s)/gm, '$1')
    // Convert plain newlines within content to proper paragraph breaks where needed
    .replace(/([^.\n])\n([A-Z])/g, '$1\n\n$2');
  
  const html = conv.makeHtml(processedText);
  
  // Post-process HTML to ensure proper list rendering with inline bullets and content
  let finalHtml = html
    .replace(/>\s+</g, '><')  // Remove whitespace between tags
    .replace(/\n\s*\n\s*\n/g, '\n\n')  // Remove excessive empty lines but keep double breaks
    .replace(/^\s+|\s+$/g, '')  // Trim start and end
    .replace(/<p><br><\/p>/g, '')  // Remove empty paragraphs with just line breaks
    .replace(/<p>\s*<\/p>/g, '')  // Remove empty paragraphs
    // Ensure proper spacing around lists (both ul and ol)
    .replace(/<\/p><ul>/g, '</p><ul>')
    .replace(/<\/ul><p>/g, '</ul><p>')
    .replace(/<\/p><ol>/g, '</p><ol>')
    .replace(/<\/ol><p>/g, '</ol><p>')
    // Fix list items that might have extra paragraph tags inside (both ul and ol)
    .replace(/<li><p>([^<]+)<\/p><\/li>/g, '<li>$1</li>')
    // Ensure list items content is inline
    .replace(/<li>\s*([^<]+)\s*<\/li>/g, '<li>$1</li>');
  
  return finalHtml;
};


export function findValueOfProperty(obj, propertyName) {
  let reg = new RegExp(propertyName, "i"); // "i" to make it case insensitive
  return Object.keys(obj).reduce((result, key) => {
    if (reg.test(key)) result.push(obj[key]);
    return result;
  }, []);
}

export const getTitleForRelationship = (relationship) => {
  if (relationship == "hasChild"){
    return "Child Nodes";
  }
  if (relationship == "interfacesWith"){
    return "Interfaces";
  }
  return relationship;
}

export function groupRelationshipsByType(relationships) {

  return relationships.reduce((acc, relationship) => {
    if (!acc[relationship.type]) {
      acc[relationship.type] = [];
    }
    acc[relationship.type].push(relationship);
    return acc;
  }, {});
}

export const updateSearchParams = (router, pathname, searchParams, paramKey, paramValue) => {
  const newParams = new URLSearchParams(searchParams);
  newParams.set(paramKey, paramValue);
  router.replace(`${pathname}?${newParams.toString()}`);
};

//will avoid going back to code generation or discussion panel after pressing back button by instructing browser to go 2 pages back
export const updateSessionStorageBackHistory = (steps = 2) => {
  if(sessionStorage.getItem("querySet")){
    let backSteps = Number(sessionStorage.getItem("querySet"));
    backSteps -= steps;
    sessionStorage.setItem("querySet", backSteps);
  }
  else{
    sessionStorage.setItem("querySet", "-3");
  }
}
