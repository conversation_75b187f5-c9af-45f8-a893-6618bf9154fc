"use client";

import React, { useEffect, useState, useContext } from 'react';
import Image from 'next/image';
import useLocalStorage from "@/hooks/useLocalStorage";
import { useUser } from '@/components/Context/UserContext';
import { TopBarContext } from '@/components/Context/TopBarContext';
import Cookies from 'js-cookie';
import kavia<PERSON>ogo from "@/../public/logo/kavia_logo.svg";
import NextJSImage from "@/../public/images/nextjs.svg";
import ReactJSImage from "@/../public/images/react.svg";
import VueImage from "@/../public/images/vue.svg";
import AngularImage from "@/../public/images/angular_logo.svg"
import NuxtImage from "@/../public/images/nuxt_logo.svg"
import RemixImage from "@/../public/images/remix_logo.svg"
import RemotionImage from "@/../public/images/remotion_logo.png"
import SlidevImage from "@/../public/images/slidev_logo.svg"
import SvelteImage from "@/../public/images/svelte_logo.svg"
// import TypescriptImage from "@/../public/images/ts_logo.svg"
import ViteImage from "@/../public/images/vite_logo.svg"
import QwikImage from "@/../public/images/qwik_logo.svg"
import AstroImage from "@/../public/images/astro_logo.svg"
import AndroidImage from "@/../public/images/android_logo.svg"
import KotlinImage from "@/../public/images/kotlin_logo.svg"

import FlutterImage from "@/../public/images/flutter_logo.svg"
import FlaskImage from "@/../public/images/flask_logo.svg"
import FastAPIImage from "@/../public/images/fastapi.svg"
import DjangoImage from "@/../public/images/django_logo.svg"
import ExpressImage from "@/../public/images/expressjs_logo.svg"
import AppTypeSwitch from './build/SimpleEnterpriceSwitch';
import TextInput from './build/TextInput';
import BuildOptionsButtons from './build/BuildOptionsButtons';
import ProjectSelectionComponent from './build/ProjectSelectionComponent';
import StackOptions from './build/StackOptions';
import ProjectCreationModal from './build/ProjectCreationFlow';

import { fetchProjectBlueprint } from '@/services/projectService';
import { useRouter } from 'next/navigation';
import { AlertContext } from './NotificationAlertService/AlertList';


export const frameworks = [
  { key: "react", label: 'React JS', icon: ReactJSImage, type: 'web', isDefault: true },
  { key: "angular", label: 'Angular', icon: AngularImage, type: 'web' },
  { key: "astro", label: 'Astro', icon: AstroImage, type: 'web' },
  { key: "nextjs", label: 'Next JS', icon: NextJSImage, type: 'web' },
  { key: "qwik", label: 'Qwik', icon: QwikImage, type: 'web' },
  { key: "nuxt", label: 'Nuxt', icon: NuxtImage, type: 'web' },
  { key: "remix", label: 'Remix', icon: RemixImage, type: 'web' },
  { key: "remotion", label: 'Remotion', icon: RemotionImage, type: 'web' },
  { key: "slidev", label: 'Slidev', icon: SlidevImage, type: 'web' },
  { key: "svelte", label: 'Svelte', icon: SvelteImage, type: 'web' },
  { key: "vite", label: 'Vite', icon: ViteImage, type: 'web' },
  { key: "vue", label: 'Vue', icon: VueImage, type: 'web' },
  { key: "flutter", label: 'Flutter', icon: FlutterImage, type: 'mobile', isDefault: true },
  { key: "android", label: 'Android', icon: AndroidImage, type: ['mobile', 'native-app']},
  { key: "kotlin", label: 'Kotlin', icon: KotlinImage, type: 'mobile' },
  { key: "flask", label: 'Flask', icon: FlaskImage, type: 'backend' },
  { key: "fastapi", label: 'FastAPI', icon: FastAPIImage, type: 'backend', isDefault: true },
  { key: "django", label: 'Django', icon: DjangoImage, type: 'backend' },
  { key: "express", label: 'Express.js', icon: ExpressImage, type: 'backend' },
];

// Build options definition (should match your BuildOptionsButtons component)
const buildOptions = [
  { id: 'web', label: 'Web' },
  { id: 'mobile', label: 'Mobile' }, 
  { id: 'backend', label: 'Backend' },
  { id: 'fullstack', label: 'Full Stack' }
];

const appTypeOptions = [
  { label: 'Apps' },
  { label: 'Projects' }
];

const getReactDefaultIndex = () => {
  return frameworks.findIndex(framework => framework.key === 'react');
};

const BuildContent = ({
  loggedInState,
  selectedType,
  setSelectedType,
  selectedBuildOption,
  setBuildOption,
  activeFramework,
  setActiveFramework,
  createProject: parentCreateProject,
  handleComplexProjectSubmit,
  isComplexProjectSubmitting,
  setIsModalOpen,
  isStreaming,
  loadingText,
  inputText,
  setInputText,
}) => {
  const [prompt, setPrompt] = useLocalStorage("prompt", "");
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);
  const [isProjectCreationModalOpen, setIsProjectCreationModalOpen] = useState(false);

  const [blueprintData, setBlueprintData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isImplementing, setIsImplementing] = useState(false);
  const router = useRouter();
  const idToken = Cookies.get('idToken');
  const userId = Cookies.get('userId');
  const {showAlert} = useContext(AlertContext)
  const reactDefaultIndex = getReactDefaultIndex();

  // Enhanced state persistence using localStorage
  const [persistedInputText, setPersistedInputText] = useLocalStorage("user_input_text", "");
  const [persistedFramework, setPersistedFramework] = useLocalStorage("selected_framework", { web: reactDefaultIndex });
  const [persistedBuildOption, setPersistedBuildOption] = useLocalStorage("selected_build_option", 0);
  const [persistedAppType, setPersistedAppType] = useLocalStorage("selected_app_type", 0);

  // Determine if light theme should be used
  const isLight = false; // Always dark mode

  // State for greeting to avoid hydration errors
  const [greeting, setGreeting] = useState("Welcome");

  // Update greeting on client-side only
  useEffect(() => {
    const hour = new Date().getHours();
    let timeGreeting = "Welcome";
    if (hour < 12) timeGreeting = "Good morning";
    else if (hour < 18) timeGreeting = "Good afternoon";
    else timeGreeting = "Good evening";

    setGreeting(timeGreeting);
  }, []);

  // Load persisted state on mount
  useEffect(() => {
    if (persistedInputText) {
      setInputText(persistedInputText);
    }

    if (persistedFramework !== null && persistedFramework !== undefined) {
      setActiveFramework(persistedFramework);
    }

    if (persistedBuildOption !== null && persistedBuildOption !== undefined) {
      // Handle both old format (array) and new format (number)
      const buildOptionValue = Array.isArray(persistedBuildOption) ? persistedBuildOption[0] || 0 : persistedBuildOption;
      setBuildOption(buildOptionValue);
    }
    
    if (persistedAppType !== null && persistedAppType !== undefined) {
      setSelectedType(persistedAppType);
    }

    // Handle legacy prompt format
    if (prompt && !persistedInputText) {
      try {
        const promptObj = JSON.parse(prompt);
        if (promptObj['requirement']) {
          setInputText(promptObj['requirement']);
          setPersistedInputText(promptObj['requirement']);
        }
        if (promptObj['framework']) {
          const frameworkIndex = promptObj['framework'] === 'React JS' ? 1 :
            promptObj['framework'] === 'Vue JS' ? 2 : 0;
          setActiveFramework({ web: frameworkIndex });
          setPersistedFramework({ web: frameworkIndex });
        }
      } catch (error) {
        console.warn('Error parsing legacy prompt:', error);
      }
    }
  }, []);

  // Sync legacy prompt on mount
  useEffect(() => {
    if (prompt) {
      try {
        const promptObj = JSON.parse(prompt);
        setInputText(promptObj['requirement']);
        setActiveFramework(promptObj['framework'] === 'React JS' ? 1 :
          promptObj['framework'] === 'Vue JS' ? 2 : 0);
      } catch (error) {
        // Handle error silently
      }
    }
  }, []);

  // State persistence effects
  useEffect(() => {
    if (inputText !== persistedInputText) {
      setPersistedInputText(inputText);
    }
  }, [inputText]);

  useEffect(() => {
    if (JSON.stringify(activeFramework) !== JSON.stringify(persistedFramework)) {
      setPersistedFramework(activeFramework);
    }
  }, [activeFramework]);

  useEffect(() => {
    if (selectedBuildOption !== persistedBuildOption) {
      const buildOptionValue = Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption;
      setPersistedBuildOption(buildOptionValue);
    }
  }, [selectedBuildOption]);

  useEffect(() => {
    if (selectedType !== persistedAppType) {
      setPersistedAppType(selectedType);
    }
  }, [selectedType]);

  const { name } = useUser();

  // Check if user is logged in
  const isLoggedIn = () => {
    const idToken = Cookies.get('idToken');
    return !!idToken;
  };

  // Utility function to get selected frameworks array - can be used anywhere
  const getCurrentSelectedFrameworks = () => {
    const selectedBuildOptionIndex = Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption || 0;
    const selectedBuildType = buildOptions[selectedBuildOptionIndex] ? buildOptions[selectedBuildOptionIndex].id : 'web';
    
    // Determine what build types to show based on selection
    const selectedBuildTypes = selectedBuildType === 'fullstack' ? ['frontend', 'backend'] : [selectedBuildType];
    
    let selectedFrameworks = {};
    
    if (typeof activeFramework === 'object' && activeFramework !== null) {
      selectedFrameworks = activeFramework;
    } else {
      const frameworkIndex = typeof activeFramework === 'number' ? activeFramework : 0;
      const framework = frameworks[frameworkIndex];
      if (framework) {
        const frameworkTypes = Array.isArray(framework.type) ? framework.type : [framework.type];
        frameworkTypes.forEach(type => {
          if (selectedBuildTypes.includes(type)) {
            selectedFrameworks[type] = frameworkIndex;
          } else if (selectedBuildTypes.includes('frontend') && (type === 'web' || type === 'mobile')) {
            // Map web/mobile to frontend for fullstack mode
            selectedFrameworks['frontend'] = frameworkIndex;
          }
        });
      }
    }
    
    return getSelectedFrameworksArray(selectedBuildTypes, selectedFrameworks);
  };

  // Function to get selected frameworks as an array
  const getSelectedFrameworksArray = (selectedBuildTypes, selectedFrameworks) => {
    const frameworkNames = [];
    
    selectedBuildTypes.forEach(buildType => {
      const frameworkIndex = selectedFrameworks[buildType];
      if (frameworkIndex !== undefined) {
        const framework = frameworks[frameworkIndex];
        if (framework) {
          frameworkNames.push(framework.label);
        }
      }
    });
    
    return frameworkNames;
  };

  const handleSubmitAndFetchBlueprint = async () => {
    setIsLoading(true);
    if (!idToken || !userId) {
      setPersistedInputText(inputText);
      setPersistedFramework(activeFramework);
      setPersistedBuildOption(Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption);
      setPersistedAppType(selectedType);

      showAlert("Access denied. Please create an account or log in to proceed.", "info");
      router.push('/users/sign_up');
      return;
    }
    
    try {
      // Handle single selection format
      const selectedBuildOptionIndex = Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption || 0;
      const selectedBuildType = buildOptions[selectedBuildOptionIndex] ? buildOptions[selectedBuildOptionIndex].id : 'web';

      // Handle framework selection based on new format
      let selectedFrameworks = {};
      
      if (typeof activeFramework === 'object' && activeFramework !== null) {
        // New format: object with build type keys
        selectedFrameworks = activeFramework;
      } else {
        // Legacy format: single number - convert to new format
        const frameworkIndex = typeof activeFramework === 'number' ? activeFramework : 0;
        const framework = frameworks[frameworkIndex] || frameworks.find(f => f.isDefault);
        
        if (framework) {
          const frameworkTypes = Array.isArray(framework.type) ? framework.type : [framework.type];
          frameworkTypes.forEach(type => {
            if (selectedBuildType === 'fullstack') {
              if (type === 'web' || type === 'mobile') {
                selectedFrameworks['frontend'] = frameworkIndex;
              } else if (type === 'backend') {
                selectedFrameworks['backend'] = frameworkIndex;
              }
            } else if (type === selectedBuildType) {
              selectedFrameworks[selectedBuildType] = frameworkIndex;
            }
          });
        }
      }

      // Initialize variables
      let frontendFramework = "";
      let backendFramework = "";
      let platform = [];

      if (selectedBuildType === 'fullstack') {
        // For fullstack, handle frontend and backend separately
        const frontendFrameworkIndex = selectedFrameworks['frontend'];
        const backendFrameworkIndex = selectedFrameworks['backend'];
        
        if (frontendFrameworkIndex !== undefined) {
          const fw = frameworks[frontendFrameworkIndex];
          if (fw) {
            frontendFramework = fw.label;
            // Determine the frontend platform type (web or mobile)
            const frameworkType = Array.isArray(fw.type) ? fw.type[0] : fw.type;
            if (frameworkType === 'mobile' || (Array.isArray(fw.type) && fw.type.includes('mobile'))) {
              platform.push('mobile');
            } else if (frameworkType === 'web' || (Array.isArray(fw.type) && fw.type.includes('web'))) {
              platform.push('web');
            }
          }
        }
        
        if (backendFrameworkIndex !== undefined) {
          const bw = frameworks[backendFrameworkIndex];
          if (bw) {
            backendFramework = bw.label;
            platform.push('backend');
          }
        }
      } else {
        // For non-fullstack options
        const frameworkIndex = selectedFrameworks[selectedBuildType];
        if (frameworkIndex !== undefined) {
          const framework = frameworks[frameworkIndex];
          if (framework) {
            const frameworkType = Array.isArray(framework.type) ? framework.type[0] : framework.type;
            
            if (frameworkType === 'web' || frameworkType === 'mobile' || (Array.isArray(framework.type) && (framework.type.includes('web') || framework.type.includes('mobile')))) {
              frontendFramework = framework.label;
            } else if (frameworkType === 'backend' || (Array.isArray(framework.type) && framework.type.includes('backend'))) {
              backendFramework = framework.label;
            }
          }
        }
        
        platform.push(selectedBuildType);
      }

      // Remove duplicates from platform array
      platform = [...new Set(platform)];

      console.log('=== SUBMISSION DATA ===');
      console.log('Selected Build Type:', selectedBuildType);
      console.log('Frontend Framework:', frontendFramework);
      console.log('Backend Framework:', backendFramework);
      console.log('Platform:', platform);
      console.log('Selected Frameworks Object:', selectedFrameworks);
      console.log('Active Framework State:', activeFramework);

      // Get the mock data from our service, passing the selected framework and correct platform
      const blueprintData = await fetchProjectBlueprint(inputText, frontendFramework, backendFramework, platform);

      console.log("Blueprint Final response:", blueprintData);

      sessionStorage.setItem('generated_project_id', blueprintData?.projectInfo?.id);
      setBlueprintData(blueprintData);
      clearPersistedState();
      setIsProjectCreationModalOpen(true);
    
    } catch (error) {
      console.error("Error in handleSubmitAndFetchBlueprint:", error);
      alert("Failed to generate project blueprint. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const clearPersistedState = () => {
    setPersistedInputText("");
    setPersistedFramework({ web: 0 });
    setPersistedBuildOption(0);
    setPersistedAppType(0);
  };

  const handleStartImplementation = async (state) => {
    console.log("=== START IMPLEMENTATION ===");
    console.log("Full state object:", state);
    console.log("Project Blueprint:", state?.projectBlueprint);
    
    setIsImplementing(true);
    if (parentCreateProject) {
      try {
        // Get the most up-to-date blueprint data from the state object
        const updatedBlueprint = state?.projectBlueprint || blueprintData;

        if (!updatedBlueprint) {
          console.error('No blueprint data available');
          return;
        }

        console.log("Updated Blueprint:",updatedBlueprint);

        // Call parent function with the complete updated blueprint (not just projectInfo)
        const projectResponse = await parentCreateProject(updatedBlueprint);

        console.log("Project Response:", projectResponse);
        
        // Close the modal
        setIsProjectCreationModalOpen(false);
      } catch (error) {
        console.error('Error creating project:', error);
      } finally {
        setIsImplementing(false);
      }
    }
  };

  return (
    <div className="h-screen overflow-hidden">
      <div className="h-full flex items-center justify-center py-8">
        <div className={`w-full max-w-2xl px-4 flex flex-col items-center text-white transition-colors duration-500 ease-in-out pb-16`}>
        <div className={`w-full flex justify-center mb-6 transition-all duration-500 ease-in-out ${loggedInState ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'}`}>
          <Image
            src={kaviaLogo}
            alt={"Kavia AI"}
            className={`transition-transform duration-500 ease-in-out ${
              !loggedInState ? 'w-16 h-16 max-w-16 max-h-16' : 'size-22'
            }`}
          />
        </div>

        <div className={`text-center mb-2 transition-opacity duration-500 ease-in-out ${loggedInState ? 'opacity-100' : 'opacity-0 h-0 overflow-hidden'}`}>
          <p className={`text-gray-300 typography-heading-2 font-weight-medium transition-colors duration-500 ease-in-out`}>
            {`${greeting}, ${name || 'welcome back'}`}
          </p>
        </div>

        <h1 className={`typography-heading-1 font-weight-light text-center text-white mb-8 transition-colors duration-500 ease-in-out`}>
          What do you want to build today?
        </h1>

        <AppTypeSwitch selectedType={selectedType} setSelectedType={setSelectedType} isStreaming={isStreaming} isLight={isLight} />

        <div className="w-full min-h-[400px] flex flex-col items-center">
          {appTypeOptions[selectedType].label === "Apps" ? (
            <>
              <TextInput
                disabled={isStreaming || isLoading}
                loadingText={isLoading ? "Generating blueprint..." : loadingText}
                inputText={inputText}
                setInputText={setInputText}
                handleSubmit={handleSubmitAndFetchBlueprint}
                isLight={isLight}
              />

              <BuildOptionsButtons disabled={isStreaming} buildOption={selectedBuildOption} setBuildOption={setBuildOption} isLight={isLight} />
              {/* Scrollable container for framework options */}
              <div className="w-full overflow-y-auto max-h-[60vh] flex flex-col items-center">
                <StackOptions
                  frameworks={frameworks}
                  activeFramework={activeFramework}
                  setActiveFramework={setActiveFramework}
                  isStreaming={isStreaming}
                  isLight={isLight}
                  buildOption={selectedBuildOption}
                />
              </div>
            </>
          ) : (
            <ProjectSelectionComponent
              handleComplexProjectSubmit={handleComplexProjectSubmit}
              isComplexProjectSubmitting={isComplexProjectSubmitting}
              isLight={isLight}
              setIsModalOpen={setIsModalOpen}
            />
          )}
        </div>

        <ProjectCreationModal
          isOpen={isProjectCreationModalOpen}
          onClose={() => {
            setIsProjectCreationModalOpen(false);
            setBlueprintData(null); // Clear blueprint data when closing
          }}
          onStartImplementation={handleStartImplementation}
          initialBlueprint={blueprintData}
          frameworkOptions={frameworks}
          isImplementing={isImplementing}
        />
              </div>
      </div>
    </div>
  );
};

export default BuildContent;