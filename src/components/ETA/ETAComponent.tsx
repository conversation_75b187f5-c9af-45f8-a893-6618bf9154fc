import React from 'react';
import { Clock } from 'lucide-react';
import { ETAComponentProps } from './ETATypes';
import { getTimeColor, formatTimeDisplay } from './ETAUtils';

const ETAComponent: React.FC<ETAComponentProps> = ({
  estimatedMinutes,
  itemsSelected,
  displayText,
  className = ''
}) => {
  const colorClasses = getTimeColor(estimatedMinutes);
  const timeDisplay = displayText || formatTimeDisplay(estimatedMinutes);

  return (
    <div className={`rounded-lg border p-4 ${colorClasses} ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left side - Icon and label */}
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4" />
          <span className="text-sm font-medium">Estimated Time</span>
        </div>

        {/* Middle - Items selected */}
        <div className="text-sm">
          {itemsSelected} {itemsSelected === 1 ? 'item' : 'items'} selected
        </div>

        {/* Right side - Time display */}
        <div className="text-sm font-semibold">
          {timeDisplay}
        </div>
      </div>
    </div>
  );
};

export default ETAComponent;