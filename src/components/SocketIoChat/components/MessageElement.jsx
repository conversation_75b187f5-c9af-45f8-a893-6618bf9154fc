import React, { memo, useRef, useState, useCallback, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';
import CodebaseOperations from "../../ChatOperation/index";
import { formatDateTime } from '@/utils/datetime';

// Import CodeBlock dynamically to handle markdown properly
const CodeBlock = dynamic(() => import('../../CodeBlock'), {
  ssr: false,
});

// Memoized MessageElement component
const MessageElement = memo(({
  message,
  messages,
  activeReplyTo,
  setActiveReplyTo,
  setIsInputEnabled,
  textAreaRef,
  scrollToMessage,
  isPanelExpanded,
  getUserAvatar,
  getKaviaAvatar,
  userId,
  formatDateTime,
  renderHTML,
  deploymentStatusMap,
  fileOperationsMap,
  fileOpsUpdateCounter,
  openAccordions,
  setOpenAccordions,
  handleRollbackClick
}) => {
  const isNeedsResponse = message.status === 'needs_response' || message.status === 'pending';
  const isMessageNeedsResponse = isNeedsResponse && (message.msg_type === 'llm' || message.msg_type === 'error');
  const isHighlighted = message.id === activeReplyTo;

  // Get file operations associated with this message
  const messageOperations = fileOperationsMap[message.id] || [];
  
  // Check for file_updates directly in the message
  const hasFileUpdates = message.file_updates && Array.isArray(message.file_updates) && message.file_updates.length > 0;
  
  // Check for checkpoints in the message
  const hasCheckpoints = message.check_points && Array.isArray(message.check_points) && message.check_points.length > 0;

  // Determine if there are ongoing operations for this message
  const hasOngoingOperations = messageOperations.length > 0 &&
                               messageOperations.some(op => op.status === 'processing');

  // Store a reference to the accordion container
  const accordionContainerRef = useRef(null);
  const [accordionScrollTop, setAccordionScrollTop] = useState(0);
  const [accordionScrollHeight, setAccordionScrollHeight] = useState(0);
  const messageContainerRef = useRef(null);

  // Save scroll position before toggling
  const saveScrollPosition = useCallback(() => {
    if (accordionContainerRef.current) {
      setAccordionScrollTop(accordionContainerRef.current.scrollTop);
      setAccordionScrollHeight(accordionContainerRef.current.scrollHeight);
    }
  }, []);

  // Restore scroll position after DOM updates
  useEffect(() => {
    if (accordionContainerRef.current && openAccordions[message.id]) {
      // Use RAF to ensure DOM is updated
      requestAnimationFrame(() => {
        if (accordionContainerRef.current) {
          // Special handling for scroll restoration
          if (accordionScrollHeight > 0) {
            // Calculate if user was at bottom
            const wasAtBottom = Math.abs((accordionScrollTop + accordionContainerRef.current.clientHeight) - accordionScrollHeight) < 5;

            if (wasAtBottom) {
              // Scroll to bottom
              accordionContainerRef.current.scrollTop = accordionContainerRef.current.scrollHeight;
            } else {
              // Maintain scroll position
              accordionContainerRef.current.scrollTop = accordionScrollTop;
            }
          }
        }
      });
    }
  }, [messageOperations, openAccordions, message.id, accordionScrollTop, accordionScrollHeight]);

  const renderAttachment = (attachment, index) => {
    return (
      <div
        key={index}
        className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs"
      >
        <div className="text-blue-500 mr-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
          </svg>
        </div>
        <span className="truncate max-w-[120px]">
          {attachment.filename || attachment.originalName || 'Attachment'}
        </span>
      </div>
    );
  };

  // Handle system messages
  if ((message.msg_type === "SYSTEM" || message.msg_type === "system") && message.type !== "deployment_status") {
    return null;
  }

  // Handle deployment status messages
  if (message.type === "deployment_status") {
    // Get deployment data from message or from map
    const deploymentData = message.deployment_data || deploymentStatusMap[message.deployment_data?.id] || {};
    const status = deploymentData.status || 'pending';

    // Status to icon/color mapping - using the DeploymentInterface.tsx theme colors
    const statusConfig = {
      success: { color: 'text-green-600', bg: 'bg-green-50' },
      failed: { color: 'text-red-600', bg: 'bg-red-50' },
      default: { color: 'text-orange-500', bg: 'bg-orange-50' }
    };

    // Get config based on status or use default
    const config = statusConfig[status.toLowerCase()] || statusConfig.default;

    // Show loader for in-progress states
    const showLoader = ['pending', 'in_progress', 'deploying', 'processing'].includes(status.toLowerCase());

    // Extract app URL if available
    const appUrl = deploymentData.app_url && status.toLowerCase() === 'success'
      ? deploymentData.app_url
      : null;

    // Get appropriate label based on status
    let statusLabel = status.charAt(0).toUpperCase() + status.slice(1);
    // Add custom detail for different states
    let statusDetail = '';

    if (status.toLowerCase() === 'deploying' || status.toLowerCase() === 'processing') {
      statusDetail = 'Building your application...';
    } else if (status.toLowerCase() === 'success') {
      statusDetail = 'Your application has been deployed successfully!';
    } else if (status.toLowerCase() === 'failed') {
      statusDetail = 'Deployment failed. Please check the logs.';
    }

    return (
      <div id={`message-wrapper-${message.id}`} className="mb-4">
        <div className="flex justify-start ml-2 max-w-[85%]">
          <div className={`flex flex-col rounded-lg p-2.5 ${config.bg} w-full`}>
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-2 mt-1">
                {showLoader ? (
                  <div className="scale-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500 animate-rocket">
                      <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" fill="#F26A1B" />
                      <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" fill="#F26A1B" />
                      <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" fill="#F26A1B" />
                      <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" fill="#F26A1B" />
                    </svg>
                  </div>
                ) : status.toLowerCase() === 'success' ? (
                  <div className="scale-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-600">
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                ) : status.toLowerCase() === 'failed' ? (
                  <div className="scale-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
                      <circle cx="12" cy="12" r="10" />
                      <line x1="15" y1="9" x2="9" y2="15" />
                      <line x1="9" y1="9" x2="15" y2="15" />
                    </svg>
                  </div>
                ) : (
                  <div className="scale-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
                      <circle cx="12" cy="12" r="10" />
                      <line x1="12" y1="8" x2="12" y2="12" />
                      <line x1="12" y1="16" x2="12.01" y2="16" />
                    </svg>
                  </div>
                )}
              </div>
              <div className="flex flex-col">
                <div className={`font-medium text-sm ${config.color}`}>
                  Deployment {statusLabel}
                </div>
                {statusDetail && (
                  <div className="text-gray-500 text-xs mt-0.5 mb-0.5 leading-tight">
                    {statusDetail}
                  </div>
                )}
              </div>
            </div>

            {/* App URL link if available */}
            {appUrl && (
              <div className="mt-1.5 ml-7">
                <a href={appUrl}
                   target="_blank"
                   rel="noopener noreferrer"
                   className="inline-flex items-center px-2.5 py-1.5 bg-white rounded-md text-xs text-green-600 hover:text-green-700 border border-green-200 hover:bg-green-50 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15 3 21 3 21 9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                  </svg>
                  Visit Deployed App
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  const handleMessageClick = () => {
    if (isMessageNeedsResponse) {
      setActiveReplyTo(message.id);
      setIsInputEnabled(true);
      if (textAreaRef.current) {
        textAreaRef.current.focus();
      }
    }
  };

  // Hooks for file operations, moved to the top level to respect the Rules of Hooks.
  const isOpen = !!openAccordions[message.id];

  const handleToggleAccordion = useCallback((newState) => {
    saveScrollPosition();
    if (newState) {
      setOpenAccordions({ ...openAccordions, [message.id]: true });
    } else {
      const newOpenAccordions = { ...openAccordions };
      delete newOpenAccordions[message.id];
      setOpenAccordions(newOpenAccordions);
    }
  }, [message.id, saveScrollPosition, openAccordions, setOpenAccordions]);

  const operations = useMemo(() => {
    if (hasFileUpdates) {
      return message.file_updates.map(update => ({
        id: `${update.message_id || message.id}-${update.file_path || update.file_name}`,
        status: 'completed',
        label: update.operation || 'write',
        path: update.file_path || update.file_name,
        operation: update.operation,
        content: update.content,
        timestamp: update.timestamp,
        sender: update.sender
      }));
    }
    return messageOperations;
  }, [message, hasFileUpdates, messageOperations]);

  let title = "Codebase changes";
  if (hasOngoingOperations) {
    title = "File operations in progress";
  } else if (operations.length > 0) {
    title = "Completed codebase changes";
  }

  const MemoizedCodebaseOperations = useMemo(() => {
    if (!operations || operations.length === 0) {
      return null;
    }
    const capitalizeFirstLetter = (str) => {
      if (!str) return str;
      return str.charAt(0).toUpperCase() + str.slice(1);
    };

    const stableOperations = operations.map(op => ({
      id: op.id || `op-${Math.random().toString(36).substring(2, 10)}`,
      status: op.status || "processing",
      label:  capitalizeFirstLetter(op.label || op.status || "Operation"),
      path: op.path || "Unknown path"
    }));

    return (
      <CodebaseOperations
        key={`codebase-ops-${message.id}`}
        title={title}
        operations={stableOperations}
        isCollapsed={!isOpen}
        onCollapseToggle={handleToggleAccordion}
        allowInteractionDuringStreaming={true}
      />
    );
  }, [operations, isOpen, title, message.id, handleToggleAccordion]);

  const renderCheckpoints = () => {
    if (!hasCheckpoints) return null;
    return (
      <div className="mt-2">
        <div className="text-sm font-medium mb-1">Git Checkpoints:</div>
        <div className="bg-gray-50 p-2 rounded text-xs">
          {message.check_points.map((checkpoint, idx) => (
            <div key={idx} className="mb-1 pb-1 border-b border-gray-200 last:border-b-0">
              <div className="font-medium">{checkpoint.message}</div>
              <div className="text-gray-600 flex flex-wrap gap-x-4">
                <span>Hash: {checkpoint.hash.substring(0, 7)}</span>
                <span>Date: {new Date(checkpoint.date).toLocaleString()}</span>
                <span>Author: {checkpoint.author}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Check if there's a next message (indicates current message is completed)
  const hasNextMessage = useMemo(() => {
    const currentIndex = messages.findIndex(m => m.id === message.id);
    return currentIndex !== -1 && currentIndex < messages.length - 1;
  }, [messages, message.id]);

  // Check if this is the last message (could be streaming)
  const isLastMessage = useMemo(() => {
    const currentIndex = messages.findIndex(m => m.id === message.id);
    return currentIndex === messages.length - 1;
  }, [messages, message.id]);

  // Optimize child message rendering with memoization
  const childMessages = useMemo(() => {
    return messages
      .filter(m => m.parent_id === message.id)
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }, [messages, message.id]);

  // Update the message info display
  const renderMessageInfo = () => {
    const fileUpdatesCount = hasFileUpdates ? message.file_updates.length : 
                            (fileOperationsMap[message.id]?.length || 0);
    
    const checkpointsCount = hasCheckpoints ? message.check_points.length : 0;
    
    if (fileUpdatesCount === 0 && checkpointsCount === 0) return null;
    
    return (
      <div className="mt-1 text-xs font-medium">
        {fileUpdatesCount > 0 && (
          <div className="text-green-600">
            🖥️ {fileUpdatesCount} file operation(s)
          </div>
        )}
        {checkpointsCount > 0 && (
          <div className="text-blue-600">
            📌 {checkpointsCount} git checkpoint(s)
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      id={`message-wrapper-${message.id}`}
      className="mb-4 message-container"
      ref={messageContainerRef}
    >
      {/* USER MESSAGE - Right aligned */}
      {message.msg_type === 'user' && (
        <div className="flex justify-end items-end mb-4 w-full">
          {/* Message content with Figma styling */}
          <div
            className="px-3 py-2 bg-[#faeee4] rounded-tl-xl rounded-tr-xl rounded-bl-xl outline outline-1 outline-offset-[-1px] outline-[#faebe0]/90 flex flex-col justify-start items-start overflow-hidden mr-2 max-w-[80%]"
            onClick={handleMessageClick}
          >
            <div
              className="self-stretch text-[#1f2a37] text-[13px] font-normal font-['Hind'] leading-snug whitespace-pre-line message-content"
              dangerouslySetInnerHTML={{ __html: renderHTML(message.content) }}
            />
          </div>

          {/* User avatar - vertically aligned to bottom */}
          <div className="w-6 h-6 bg-[#6875f5] rounded-[32px] outline outline-1 outline-offset-[-1px] outline-[#5850ec] flex justify-center items-center overflow-hidden flex-shrink-0">
            {getUserAvatar(message?.user_details?.name, userId)}
          </div>
        </div>
      )}

      {message.attachments && message.attachments.length > 0 && (
        <div className="mt-2 flex justify-end">
          <div className="flex flex-wrap gap-2">
            {message.attachments.map((attachment, index) => renderAttachment(attachment, index))}
          </div>
        </div>
      )}

      {/* AI/LLM MESSAGE - Left aligned */}
      {message.msg_type === 'llm' && (
        <div className="flex flex-col justify-start mb-4 ml-1 max-w-full">
          {/* AI message with avatar and content */}
          <div className="flex max-w-full">
            <div className="flex-shrink-0 mr-2">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clipPath="url(#clip0_204_169347)">
                  <g filter="url(#filter0_d_204_169347)">
                    <path d="M19.6079 14.7872C17.6453 13.998 15.7347 13.0931 13.864 12.1115C13.7202 12.0351 13.577 11.9574 13.4343 11.879C15.4316 10.7872 17.4977 9.82389 19.6079 8.96641C20.5449 8.61552 20.8802 7.38299 20.2397 6.60873C19.7171 5.95481 18.7634 5.84831 18.1095 6.37092C16.4448 7.67591 14.7059 8.87814 12.9203 10.0075C12.7821 10.094 12.6434 10.179 12.5039 10.2631C12.5571 7.9875 12.7558 5.71656 13.0683 3.46033C13.2329 2.47351 12.3333 1.56685 11.3423 1.73439C10.5433 1.8556 9.98551 2.58023 10.061 3.37469C10.6267 5.88366 10.9512 8.47125 11.0035 11.117C11.0057 11.2253 11.0077 11.3335 11.009 11.4418C11.0103 11.565 11.0114 11.6884 11.0116 11.8118C11.0116 11.8333 11.0121 11.8548 11.0121 11.8766C11.0121 11.8965 11.0118 11.9165 11.0116 11.9367C11.0116 11.9383 11.0116 11.9398 11.0116 11.9413C11.0116 12.0738 11.0103 12.2057 11.0088 12.3377C11.0086 12.3568 11.0081 12.3757 11.0079 12.3946C11.0072 12.4427 11.0066 12.4907 11.0057 12.5388C10.9598 15.2182 10.6335 17.8385 10.061 20.3782C9.98551 21.1729 10.5435 21.8973 11.3423 22.0185C12.3331 22.1861 13.2327 21.2796 13.0683 20.2926C12.756 18.0379 12.5573 15.7683 12.5039 13.4942C14.4466 14.6774 16.3128 15.9841 18.1093 17.382C18.8815 18.0179 20.1167 17.6923 20.4669 16.7502C20.7719 15.9707 20.3872 15.0915 19.6077 14.7867L19.6079 14.7872Z" fill="#F26A1B" />
                    <path d="M6.70309 13.4001C6.44266 13.5221 6.1807 13.6418 5.91786 13.7599C5.12779 14.1146 4.33706 14.467 3.53163 14.7872C2.59817 15.1392 2.26419 16.3666 2.9012 17.1402C3.42228 17.795 4.37527 17.9035 5.03007 17.3824C5.25691 17.2019 5.48681 17.0269 5.71737 16.8526C5.94882 16.6802 6.17982 16.5067 6.41324 16.3377C6.8781 15.9971 7.34494 15.6591 7.81726 15.3295C8.91211 14.5237 7.95977 12.8537 6.70309 13.3998V13.4001Z" fill="#F26A1B" />
                    <path d="M7.8173 8.4238C7.58125 8.25933 7.34673 8.09223 7.11287 7.9238C6.41086 7.41678 5.71017 6.90845 5.03011 6.3709C4.25849 5.7385 3.02838 6.06304 2.67704 7.00133C2.3705 7.77998 2.75302 8.65963 3.53167 8.96617C3.80132 9.07245 4.06789 9.184 4.33403 9.29643C4.59907 9.41061 4.86477 9.52392 5.12783 9.64162C5.65527 9.87394 6.18139 10.1093 6.70291 10.3535C7.94817 10.8987 8.9183 9.2389 7.81708 8.4238H7.8173Z" fill="#F26A1B" />
                  </g>
                </g>
                <defs>
                  <filter id="filter0_d_204_169347" x="-3" y="-1" width="30" height="30" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="2" />
                    <feGaussianBlur stdDeviation="1.5" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_204_169347" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_204_169347" result="shape" />
                  </filter>
                  <clipPath id="clip0_204_169347">
                    <rect width="24" height="24" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
            <div className='max-w-[80%]'>
              <span className="font-hind font-medium text-[13px] leading-[165%] tracking-[0%] text-gray-800 block mb-1">KaviaAI</span>
              <div
                className="whitespace-pre-line font-['Hind'] text-[13px] leading-[165%] tracking-normal font-normal text-[#1F2A37] bg-white pl-1 rounded-lg message-content"
                dangerouslySetInnerHTML={{ __html: renderHTML(message.content) }}
              />

              {/* Rollback Button - Only show after streaming is completed and if message has operations or checkpoints */}
              {!isNeedsResponse && !hasOngoingOperations && !isLastMessage && (messageOperations.length > 0 || hasCheckpoints) && (
                <div className="mt-2 flex justify-start">
                  <button
                    onClick={() => handleRollbackClick(message.id)}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded hover:bg-gray-100 hover:text-gray-700 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50"
                    title="Rollback to this point and stash current changes"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                      <path d="M3 3v5h5" />
                    </svg>
                    Rollback
                  </button>
                </div>
              )}

              {/* Render file operations using the function */}
              <div ref={accordionContainerRef}>
                {((operations && operations.length > 0) || hasCheckpoints) && (
                  <div className="mt-2 pl-[26px] w-full overflow-x-hidden">
                    {MemoizedCodebaseOperations}
                    {renderCheckpoints()}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* ERROR MESSAGE */}
      {message.msg_type === 'error' && (
        <div className="flex justify-start mb-4">
          <div className="bg-red-50 p-3 rounded-lg max-w-[80%]">
            <div className="whitespace-pre-line font-['Hind'] text-[13px] leading-[165%] tracking-normal font-normal text-[#FF0000] message-content"
              style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}
              dangerouslySetInnerHTML={{ __html: renderHTML(message.content) }}
            />
          </div>
        </div>
      )}

      {/* Render child messages */}
      <div className="max-w-full">
        {childMessages.map(childMessage => (
          <MessageElement
            key={childMessage.id}
            message={childMessage}
            messages={messages}
            activeReplyTo={activeReplyTo}
            setActiveReplyTo={setActiveReplyTo}
            setIsInputEnabled={setIsInputEnabled}
            textAreaRef={textAreaRef}
            scrollToMessage={scrollToMessage}
            isPanelExpanded={isPanelExpanded}
            getUserAvatar={getUserAvatar}
            getKaviaAvatar={getKaviaAvatar}
            userId={userId}
            formatDateTime={formatDateTime}
            renderHTML={renderHTML}
            deploymentStatusMap={deploymentStatusMap}
            fileOperationsMap={fileOperationsMap}
            fileOpsUpdateCounter={fileOpsUpdateCounter}
            openAccordions={openAccordions}
            setOpenAccordions={setOpenAccordions}
            handleRollbackClick={handleRollbackClick}
          />
        ))}
      </div>

      {/* Render message info */}
      {renderMessageInfo()}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  // Only re-render if something important changed

  // Always re-render when fileOpsUpdateCounter changes
  if (prevProps.fileOpsUpdateCounter !== nextProps.fileOpsUpdateCounter) {
    return false; // false means do re-render
  }

  // Check if this message has file operations
  const prevOps = prevProps.fileOperationsMap[prevProps.message.id] || [];
  const nextOps = nextProps.fileOperationsMap[nextProps.message.id] || [];

  // If operations count changed, re-render
  if (prevOps.length !== nextOps.length) {
    return false; // do re-render
  }

  // Check if file_updates changed
  const prevFileUpdates = prevProps.message.file_updates || [];
  const nextFileUpdates = nextProps.message.file_updates || [];
  if (prevFileUpdates.length !== nextFileUpdates.length) {
    return false; // do re-render
  }

  // Check if check_points changed
  const prevCheckpoints = prevProps.message.check_points || [];
  const nextCheckpoints = nextProps.message.check_points || [];
  if (prevCheckpoints.length !== nextCheckpoints.length) {
    return false; // do re-render
  }

  //If message content got updated
  if (prevProps.message.content !== nextProps.message.content) {
    return false; // re-render because content changed
  }

  // Standard checks
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.activeReplyTo === nextProps.activeReplyTo
  );
});

MessageElement.displayName = "MessageElement";

export default MessageElement; 