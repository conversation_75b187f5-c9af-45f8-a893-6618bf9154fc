import { useEffect, useCallback } from 'react';

export const useWebSocketMessages = ({
  wsConnection,
  handleInitialMessages,
  safeAddOrUpdateMessageInState,
  autoScroll,
  isAtBottom,
  scrollToBottom,
  isManualScrollingStillActive,
  setIsAiTyping,
  setIsReady,
  updateFileOperations,
  setMessages,
  messages,
  fileOperationsMap,
  setFileOperationsMap,
  openAccordions,
  setOpenAccordions,
  pendingFileOperations,
  handleRollbackStatusUpdate,
  processMessageFileUpdates
}) => {

  useEffect(() => {
    if (!wsConnection) return;

    // Add connection error handling
    const handleConnectionError = (error) => {
      setIsAiTyping(false);
      // Show error message to user
      if (setMessages && typeof setMessages === 'function') {
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          const errorMessage = "Connection error. Please refresh the page and try again.";

          if (lastMessage && lastMessage.content === errorMessage) {
            return prev;
          }

          return [
            ...prev,
            {
              id: Date.now(),
              content: errorMessage,
              msg_type: "error",
              timestamp: new Date().toISOString()
            }
          ];
        });
      }
    };

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Auto-scroll for important message events
        const shouldAutoScroll = ['message_received', 'message_chunk', 'message_resolved', 'agent_message'].includes(data.type);

        if (shouldAutoScroll && autoScroll && isAtBottom()) {
          // Use setTimeout to scroll after DOM updates
          setTimeout(() => scrollToBottom(), 50);
        }

        switch (data.type) {
          case "ready_check":
            setIsReady(true);
            break;

          case 'initial_messages':
            handleInitialMessages(data.data);
            
            // Process file_updates for each message
            if (Array.isArray(data.data)) {
              data.data.forEach(message => {
                if (message && message.file_updates) {
                  processMessageFileUpdates(message);
                }
              });
            }
            break;

          case 'file_update':
            if (data.data) {
              const { message_id, operation, file_name, is_end, id } = data.data;

              if (message_id && file_name) {
                // Find messages to attach this operation to
                const possibleMessages = messages.filter(m => m.msg_type === 'llm');

                // Try to find matching messages
                const exactMatch = possibleMessages.find(m => m.id === message_id);
                const partialMatch = !exactMatch && possibleMessages.find(m =>
                  (typeof m.id === 'string' && typeof message_id === 'string' &&
                  (m.id.includes(message_id) || message_id.includes(m.id)))
                );

                // Use the best match or most recent message
                const targetMessage = exactMatch || partialMatch ||
                  (possibleMessages.length > 0 ? possibleMessages[possibleMessages.length - 1] : null);

                if (targetMessage) {
                  // For first file operation in a message, auto-open the accordion
                  const existingOperations = fileOperationsMap[targetMessage.id] || [];
                  if (existingOperations.length === 0) {
                    // Auto-open the accordion for the first file operation
                    const updatedAccordions = {...openAccordions};
                    updatedAccordions[targetMessage.id] = true;
                    setOpenAccordions(updatedAccordions);
                  }

                  updateFileOperations(targetMessage.id, operation || 'write', file_name, !!is_end, id);
                } else {
                  // Fallback for when no message is found
                  updateFileOperations(message_id, operation || 'write', file_name, !!is_end, id);
                }
              }
            }
            break;

          case 'deployment_status':
            // Deployment status is now handled via props from CodeDiscussionPanel
            // to avoid duplicate processing. The parent component listens for
            // deployment_status messages and passes them as props to ChatInterface.
            // This prevents the same message from being processed twice.
            break;

          case 'message_received':
            // When a user message is received, prepare for AI typing
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }

            // Add message without forcing scroll
            safeAddOrUpdateMessageInState(data.data, data.type);

            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }

            // Only auto-scroll for important messages when user hasn't manually scrolled
            if (autoScroll && !isManualScrollingStillActive() && ['user', 'system_message'].includes(data.data.msg_type)) {
              // Use a slight delay to ensure message is rendered
              setTimeout(() => {
                // Double-check user hasn't scrolled during the delay
                if (!isManualScrollingStillActive()) {
                  scrollToBottom();
                }
              }, 100);
            }
            break;

          case 'message_chunk':
            // AI is typing when receiving chunks
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(true);
            }

            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }

            // Always use the batched update for chunks to reduce flickering
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'message_resolved':
            // AI finished typing
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(false);
            }
            
            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }
            
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'agent_message':
            // Handle agent messages with streaming status
            if (data.data && data.data.status === "streaming") {
              setIsAiTyping(true);

              // Look for an existing streaming message to update
              const streamingMessage = messages.find(m =>
                m.msg_type === 'llm' && m.status === 'streaming'
              );

              if (streamingMessage) {
                // Update existing streaming message with REPLACEMENT (not append)
                safeAddOrUpdateMessageInState({
                  ...streamingMessage,
                  ...data.data,
                  content: data.data.content, // Replace content for streaming agent messages
                  status: 'streaming'
                }, 'agent_message_streaming'); // Special type to indicate content replacement

                // Auto-scroll if at bottom
                if (autoScroll && isAtBottom()) {
                  setTimeout(() => scrollToBottom(), 50);
                }
              } else {
                // Create a new streaming message
                safeAddOrUpdateMessageInState({
                  ...data.data,
                  id: data.data.id || Date.now(),
                  type: "agent_message",
                  status: "streaming",
                  timestamp: data.data.timestamp || new Date().toISOString()
                }, 'agent_message_streaming');

                // Auto-scroll if at bottom
                if (autoScroll && isAtBottom()) {
                  setTimeout(() => scrollToBottom(), 50);
                }
              }
            } else if (data.data) {
              // Regular agent message handling
              safeAddOrUpdateMessageInState(data.data, data.type);

              // If this completes a message, stop AI typing indicator
              if (data.data.status === 'completed' || data.data.status === 'done') {
                setIsAiTyping(false);
                // Final scroll to bottom when message completes
                if (autoScroll) {
                  setTimeout(() => scrollToBottom(), 150);
                }
              }
            }
            break;

          case 'message_status':
          case 'message_added':
          case 'command_response':
            safeAddOrUpdateMessageInState(data.data, data.type);
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }
            break;

          case 'needs_response':
            if (data.data.requires_resolution == true) {
              setIsAiTyping(false);
            }
            break;

          case 'switch_to_checkpoints_status':
            // Handle rollback status response
            if (handleRollbackStatusUpdate) {
              handleRollbackStatusUpdate(data);
            }
            break;

          case 'error':
            setIsAiTyping(false); // Stop typing animation on error
            break;

          default:
            // For any other message types, just process normally
            if (data.data) {
              // Process file_updates if present
              if (data.data && data.data.file_updates) {
                processMessageFileUpdates(data.data);
              }
              
              safeAddOrUpdateMessageInState(data.data, data.type);
            }
            break;
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    };

    // Create stable close handler to prevent memory leaks from anonymous functions
    const handleClose = () => {
      setIsAiTyping(false);
    };

    // Register the event listeners
    wsConnection.addEventListener('message', handleMessage);
    wsConnection.addEventListener('error', handleConnectionError);
    wsConnection.addEventListener('close', handleClose);

    return () => {
      // Clean up event listeners for this specific effect
      if (wsConnection) {
        wsConnection.removeEventListener('message', handleMessage);
        wsConnection.removeEventListener('error', handleConnectionError);
        wsConnection.removeEventListener('close', handleClose);
      }
    };
  }, [
    wsConnection,
    handleInitialMessages,
    safeAddOrUpdateMessageInState,
    autoScroll,
    scrollToBottom,
    setIsAiTyping,
    setIsReady,
    updateFileOperations,
    setMessages,
    messages,
    isAtBottom,
    isManualScrollingStillActive,
    fileOperationsMap,
    openAccordions,
    setOpenAccordions,
    pendingFileOperations,
    handleRollbackStatusUpdate,
    processMessageFileUpdates
  ]);
}; 