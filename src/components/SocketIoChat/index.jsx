import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo
} from 'react';
import Cookies from 'js-cookie';
import { useCodeGeneration } from '../Context/CodeGenerationContext';
import { getUserAvatar, getKaviaAvatar } from '@/utils/avatarUtils';
import { useUser } from '@/components/Context/UserContext';
import { renderHTML } from '@/utils/helpers';
import { useSearchParams } from 'next/navigation';
import { formatDateTime } from '@/utils/datetime';
import ChatInput from "./ChatInput";
import ChatLoader from "@/components/Loaders/chatLoader";
import { useParams } from 'next/navigation';
import ConfirmationModal from '../Modal/ConfirmationModal';

// Import extracted components and hooks
import { ThreeDotLoader, RollbackProgressBar } from './components/LoadingComponents';
import { useRollback } from './hooks/useRollback';
import { useScrollManagement } from './hooks/useScrollManagement';
import { useFileOperations } from './hooks/useFileOperations';
import { useWebSocketMessages } from './hooks/useWebSocketMessages';
import MessageElement from './components/MessageElement';
import ScrollToBottomButton from './components/UI/ScrollButton';
import NewMessageIndicator from './components/UI/NewMessageIndicator';

import './styles.css';

const ChatInterface = ({ wsUrl, isPanelExpanded, deploymentStatus, availableModels: propAvailableModels, isLoadingModels: propIsLoadingModels, onModelSelect: propOnModelSelect, isStopped }) => {
  const searchParams = useSearchParams();

  // STATE VARIABLES
  const [deploymentStatusMap, setDeploymentStatusMap] = useState({});
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState([]);
  const [isInputEnabled, setIsInputEnabled] = useState(true);
  const lastMessageCountRef = useRef(0);
  const [attachments, setAttachments] = useState([]);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [uploadedAttachments, setUploadedAttachments] = useState([]);
  const [openAccordions, setOpenAccordions] = useState({});
  const [inputFocused, setInputFocused] = useState(false);
  const isComponentMountedRef = useRef(true);
  const { name, email } = useUser();
  // GET CONTEXT VALUES
  const {
    wsConnection,
    messages: initialCtxMessages,
    textAreaRef,
    messagesContainerRef,
    activeReplyTo,
    setActiveReplyTo,
    hasNewMessages,
    setHasNewMessages,
    autoScroll,
    setAutoScroll,
    isReady,
    setIsReady,
    isAiTyping,
    setIsAiTyping,
    taskStatus
  } = useCodeGeneration();

  // GET USER INFO
  const idToken = Cookies.get('idToken');
  const userId = Cookies.get('userId');

  const getDisplayName = useCallback((name, email) => {
    if (name) return name;
    if (email) {
      // Extract name from email by taking everything before the @ symbol
      return email.split('@')[0];
    }
    return 'User'; // Fallback if both name and email are null
  }, []);

  // USE EXTRACTED HOOKS
  const scrollHook = useScrollManagement(messagesContainerRef, setAutoScroll, setHasNewMessages);
  const { 
    isAtBottom, 
    scrollToBottom, 
    directScrollToBottom, 
    scrollToMessage, 
    handleScroll, 
    userManuallyScrolledRef, 
    isScrollingRef,
    isManualScrollingStillActive 
  } = scrollHook;

  const fileOpsHook = useFileOperations(messages, autoScroll, messagesContainerRef, isAtBottom);
  const {
    fileOperationsMap,
    pendingFileOperations,
    fileOpsUpdateCounter,
    updateFileOperations,
    processMessageFileUpdates
  } = fileOpsHook;

  // Process file_updates in existing messages
  useEffect(() => {
    if (messages.length > 0) {
      messages.forEach(message => {
        if (message && message.file_updates) {
          processMessageFileUpdates(message);
        }
      });
    }
  }, [messages, processMessageFileUpdates]);

  const rollbackHook = useRollback(wsConnection, searchParams);
  const {
    showRollbackModal,
    rollbackMessageId,
    isRollbackInProgress,
    handleRollbackClick,
    handleRollbackCancel,
    handleRollbackConfirm,
    handleRollbackStatusUpdate
  } = rollbackHook;

  // Update messages when initialCtxMessages changes
  useEffect(() => {
    if (messages.length === 0 && initialCtxMessages.length > 0) {
      setMessages(initialCtxMessages);
    }
  }, [initialCtxMessages, messages.length]);

  // UTILITY FUNCTIONS
  const hasMessagesNeedingResponse = useCallback(() => {
    return messages.some(msg =>
      (msg.status === 'needs_response' || msg.status === 'pending') &&
      (msg.msg_type === 'llm' || msg.msg_type === 'error')
    );
  }, [messages]);

  const setFirstMessageNeedingResponseAsActive = useCallback(() => {
    const needsResponse = messages.find(msg =>
      (msg.status === 'needs_response' || msg.status === 'pending') &&
      (msg.msg_type === 'llm' || msg.msg_type === 'error')
    );

    if (needsResponse) {
      setActiveReplyTo(needsResponse.id);
      setIsInputEnabled(true);
    }
  }, [messages, setActiveReplyTo]);

  // Handle input/reply state management
  useEffect(() => {
    if (hasMessagesNeedingResponse() && !activeReplyTo) {
      setFirstMessageNeedingResponseAsActive();
    } else if (!hasMessagesNeedingResponse() && !activeReplyTo) {
      setIsInputEnabled(true);
    }

    if (activeReplyTo) {
      const activeMessage = messages.find(msg => msg.id === activeReplyTo);
      if (!activeMessage ||
        !(activeMessage.status === 'needs_response' || activeMessage.status === 'pending')) {
        setActiveReplyTo(null);
        setIsInputEnabled(true);
      }
    }
  }, [messages, activeReplyTo, hasMessagesNeedingResponse, setFirstMessageNeedingResponseAsActive, setActiveReplyTo]);

  // Handle new messages arriving
  useEffect(() => {
    if (messages.length > lastMessageCountRef.current) {
      lastMessageCountRef.current = messages.length;

      if (autoScroll && isAtBottom()) {
        setTimeout(() => scrollToBottom(), 50);
      } else if (!autoScroll || !isAtBottom()) {
        setHasNewMessages(true);
      }
    }
  }, [messages.length, autoScroll, isAtBottom, scrollToBottom, setHasNewMessages]);

  // ADD OR UPDATE MESSAGE (Prevents duplicates)
  const addOrUpdateMessageInState = useCallback((incomingMessage, messageType) => {
    setMessages((prev) => {
      // Special handling for deployment status messages
      if (incomingMessage.type === "deployment_status" && incomingMessage.deployment_data?.id) {
        const deploymentId = incomingMessage.deployment_data.id;
        const existingDeploymentMsgIndex = prev.findIndex(
          m => m.type === "deployment_status" && m.deployment_data?.id === deploymentId
        );

        if (existingDeploymentMsgIndex >= 0) {
          const updatedList = [...prev];
          const oldMessage = updatedList[existingDeploymentMsgIndex];
          updatedList[existingDeploymentMsgIndex] = {
            ...oldMessage,
            ...incomingMessage,
            deployment_data: incomingMessage.deployment_data,
            content: incomingMessage.content,
            timestamp: incomingMessage.timestamp || oldMessage.timestamp
          };
          return updatedList;
        }
      }

      // Regular message handling
      const existingMsgIndex = prev.findIndex((m) => m.id === incomingMessage.id);
      const isNewMessage = existingMsgIndex === -1;

      let updatedList;
      if (isNewMessage) {
        if (incomingMessage.msg_type === 'system' && incomingMessage.type !== 'deployment_status') {
          return prev;
        }
        updatedList = [...prev, { ...incomingMessage }];
      } else {
        updatedList = [...prev];
        const oldMessage = updatedList[existingMsgIndex];

        let newContent;
        if (messageType === 'agent_message_streaming') {
          newContent = incomingMessage.content;
        } else if (incomingMessage.status === 'streaming') {
          // Handle streaming content with overlap detection
          if (oldMessage.content && incomingMessage.content) {
            if (oldMessage.content.includes(incomingMessage.content)) {
              newContent = oldMessage.content;
            } else if (incomingMessage.content.includes(oldMessage.content)) {
              newContent = incomingMessage.content;
            } else {
              // Find overlap to prevent duplication
              let maxOverlap = 0;
              const oldContentLength = oldMessage.content.length;
              const newContentLength = incomingMessage.content.length;

              for (let i = 1; i < Math.min(oldContentLength, newContentLength); i++) {
                const oldContentSuffix = oldMessage.content.slice(oldContentLength - i);
                const newContentPrefix = incomingMessage.content.slice(0, i);

                if (oldContentSuffix === newContentPrefix) {
                  maxOverlap = i;
                }
              }

              if (maxOverlap > 0) {
                newContent = oldMessage.content + incomingMessage.content.slice(maxOverlap);
              } else {
                newContent = oldMessage.content + incomingMessage.content;
              }
            }
          } else {
            newContent = (oldMessage.content || '') + (incomingMessage.content || '');
          }
        } else {
          newContent = incomingMessage.content;
        }

        const updatedUserDetails = incomingMessage.user_details || oldMessage.user_details;

        updatedList[existingMsgIndex] = {
          ...oldMessage,
          ...incomingMessage,
          content: newContent,
          user_details: updatedUserDetails
        };
      }

      return updatedList;
    });

    // Handle message status and reply state
    if (activeReplyTo === incomingMessage.id) {
      if (!(incomingMessage.status === 'needs_response' || incomingMessage.status === 'pending')) {
        setActiveReplyTo(null);
        setIsInputEnabled(false);
      }
    }

    if ((incomingMessage.status === 'needs_response' || incomingMessage.status === 'pending') &&
      (incomingMessage.msg_type === 'llm' || incomingMessage.msg_type === 'error')) {
      setActiveReplyTo(incomingMessage.id);
      setIsInputEnabled(true);
    }

    if (messageType === 'message_added') {
      setTimeout(() => {
        scrollToMessage(incomingMessage.id);
      }, 100);
    }
  }, [activeReplyTo, setActiveReplyTo, scrollToMessage]);

  // Batched message updates for streaming
  const [updateBatch, setUpdateBatch] = useState([]);
  const updateBatchTimeoutRef = useRef(null);

  const safeAddOrUpdateMessageInState = useCallback((incomingMessage, messageType) => {
    try {
      if (!incomingMessage || typeof incomingMessage !== 'object') {
        return;
      }

      const messageId = incomingMessage.id || Date.now();
      const safeMessage = { ...incomingMessage, id: messageId };

      // For streaming messages, use batching
      if (incomingMessage.status === 'streaming' || messageType === 'message_chunk') {
        setUpdateBatch(prev => [...prev, { message: safeMessage, type: messageType }]);

        if (updateBatchTimeoutRef.current) {
          clearTimeout(updateBatchTimeoutRef.current);
        }

        updateBatchTimeoutRef.current = setTimeout(() => {
          setUpdateBatch(current => {
            if (current.length > 0) {
              const messageMap = new Map();

              current.forEach(({ message, type }) => {
                if (!messageMap.has(message.id)) {
                  messageMap.set(message.id, { message: { ...message, content: message.content || '' }, type });
                } else {
                  const existing = messageMap.get(message.id);

                  if (existing.message.status === 'streaming' && message.status === 'streaming') {
                    const existingContent = existing.message.content || '';
                    const newContent = message.content || '';

                    if (!existingContent.includes(newContent) && !newContent.includes(existingContent)) {
                      let maxOverlap = 0;
                      for (let i = 1; i < Math.min(existingContent.length, newContent.length); i++) {
                        const oldSuffix = existingContent.slice(existingContent.length - i);
                        const newPrefix = newContent.slice(0, i);
                        if (oldSuffix === newPrefix) {
                          maxOverlap = i;
                        }
                      }

                      if (maxOverlap > 0) {
                        existing.message.content = existingContent + newContent.slice(maxOverlap);
                      } else {
                        existing.message.content = existingContent + newContent;
                      }
                    } else if (newContent.length > existingContent.length && newContent.includes(existingContent)) {
                      existing.message.content = newContent;
                    }
                  } else {
                    messageMap.set(message.id, { message, type });
                  }
                }
              });

              Array.from(messageMap.values()).forEach(({ message, type }) => {
                addOrUpdateMessageInState(message, type);
              });
            }

            return [];
          });

          if (autoScroll && isAtBottom()) {
            requestAnimationFrame(() => {
              if (messagesContainerRef.current) {
                messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
              }
            });
          } else if (!autoScroll && !hasNewMessages) {
            setHasNewMessages(true);
          }
        }, 150);
      } else {
        addOrUpdateMessageInState(safeMessage, messageType);

        if (autoScroll && isAtBottom()) {
          requestAnimationFrame(() => {
            if (messagesContainerRef.current) {
              messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
            }
          });
        } else if (!autoScroll && messageType === 'message_received') {
          setHasNewMessages(true);
        }
      }
    } catch (error) {
      console.error('Error in safeAddOrUpdateMessageInState:', error);
    }
  }, [addOrUpdateMessageInState, autoScroll, messagesContainerRef, isAtBottom, hasNewMessages, setHasNewMessages]);

  // Handle initial messages
  const handleInitialMessages = useCallback((msgs) => {
    const latestMessages = new Map();

    msgs
      .slice()
      .reverse()
      .filter(m => m.msg_type !== 'system' || m.type === 'deployment_status')
      .forEach(msg => {
        if (!latestMessages.has(msg.id)) {
          latestMessages.set(msg.id, msg);
        }
      });

    Array.from(latestMessages.values())
      .reverse()
      .forEach(msg => safeAddOrUpdateMessageInState(msg, 'initial_messages'));
  }, [safeAddOrUpdateMessageInState]);

  // Initialize autoScroll
  useEffect(() => {
    setAutoScroll(true);
    
    const scrollAttempts = [0, 100, 500];
    scrollAttempts.forEach(delay => {
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, delay);
    });
  }, [setAutoScroll, messagesContainerRef]);

  // Auto-scroll on content changes
  useEffect(() => {
    if (!messagesContainerRef.current || typeof MutationObserver === 'undefined') return;

    const observer = new MutationObserver(() => {
      if (autoScroll && !isScrollingRef.current && isAtBottom()) {
        scrollToBottom();
      }
    });

    observer.observe(messagesContainerRef.current, {
      childList: true,
      subtree: true
    });

    return () => observer.disconnect();
  }, [messagesContainerRef, autoScroll, scrollToBottom, isAtBottom, isScrollingRef]);

  // Handle deploymentStatus prop changes
  useEffect(() => {
    if (deploymentStatus && deploymentStatus.id) {
      const deploymentId = deploymentStatus.id;

      setDeploymentStatusMap(prevMap => ({
        ...prevMap,
        [deploymentId]: deploymentStatus
      }));

      let contentMessage = `🚀 Deployment Status: ${deploymentStatus.deployment_type || 'Application'} - ${deploymentStatus.status}`;
      if (deploymentStatus.app_url && deploymentStatus.status === 'success') {
        contentMessage += `\nApplication URL: ${deploymentStatus.app_url}`;
      }
      if (deploymentStatus.branch) {
        contentMessage += `\nBranch: ${deploymentStatus.branch}`;
      }

      const deploymentStatusMessage = {
        id: `deployment-${deploymentId}`,
        type: "deployment_status",
        content: contentMessage,
        sender: "System",
        deployment_data: deploymentStatus,
        timestamp: new Date().toISOString(),
        msg_type: "system_message"
      };

      setTimeout(() => {
        safeAddOrUpdateMessageInState(deploymentStatusMessage, 'deployment_status');
        setTimeout(() => {
          scrollToBottom();
        }, 200);
      }, 0);
    }
  }, [deploymentStatus, safeAddOrUpdateMessageInState, scrollToBottom]);

  // USE WEBSOCKET MESSAGES HOOK
  useWebSocketMessages({
    wsConnection,
    handleInitialMessages,
    safeAddOrUpdateMessageInState,
    autoScroll,
    isAtBottom,
    scrollToBottom,
    isManualScrollingStillActive,
    setIsAiTyping,
    setIsReady,
    updateFileOperations,
    setMessages,
    messages,
    fileOperationsMap,
    openAccordions,
    setOpenAccordions,
    pendingFileOperations,
    handleRollbackStatusUpdate,
    processMessageFileUpdates
  });

  // Handle sending messages
  const handleSendMessage = useCallback(() => {
    const text = inputValue.trim();
    const hasContent = text.length > 0 || attachments.length > 0;

    if (!hasContent) return;

    setAutoScroll(true);

    if (wsConnection?.readyState === WebSocket.OPEN) {
      const messagePayload = {
        type: 'send_message',
        content: text,
        parent_id: activeReplyTo,
        user_id: userId
      };

      if (uploadedAttachments.length > 0) {
        messagePayload.attachment_ids = uploadedAttachments.map(a => a.attachment_id);
      }

      if (attachments.length > 0) {
        messagePayload.attachments = attachments;
      }

      wsConnection.send(JSON.stringify(messagePayload));
    }

    setInputValue('');
    setAttachments([]);
    setAttachedFiles([]);
    setUploadedAttachments([]);

    if (textAreaRef.current) {
      textAreaRef.current.value = '';
      textAreaRef.current.style.height = '28px';
      textAreaRef.current.style.overflowY = 'hidden';
      textAreaRef.current.classList.remove('custom-scrollbar');
    }

    if (activeReplyTo) {
      setActiveReplyTo(null);
    }

    setTimeout(() => scrollToBottom(), 50);
  }, [
    inputValue,
    attachments,
    wsConnection,
    activeReplyTo,
    userId,
    uploadedAttachments,
    attachedFiles,
    textAreaRef,
    setActiveReplyTo,
    setAutoScroll,
    scrollToBottom
  ]);

  // Optimize the chat rendering by memoizing messages more effectively
  const renderMessages = useMemo(() => {
        
    const displayName = getDisplayName(name, email);

    return messages
      .filter(m => !m.parent_id && (m.msg_type !== "system" && m.msg_type !== "SYSTEM" || m.type === "deployment_status"))
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      .map(message => (
        <MessageElement
          key={`${message.id}-${fileOpsUpdateCounter}`}
          message={message}
          messages={messages}
          activeReplyTo={activeReplyTo}
          setActiveReplyTo={setActiveReplyTo}
          setIsInputEnabled={setIsInputEnabled}
          textAreaRef={textAreaRef}
          scrollToMessage={scrollToMessage}
          isPanelExpanded={isPanelExpanded}
          getUserAvatar={() => getUserAvatar(displayName)}
          getKaviaAvatar={getKaviaAvatar}
          userId={userId}
          formatDateTime={formatDateTime}
          renderHTML={renderHTML}
          deploymentStatusMap={deploymentStatusMap}
          fileOperationsMap={fileOperationsMap}
          fileOpsUpdateCounter={fileOpsUpdateCounter}
          openAccordions={openAccordions}
          setOpenAccordions={setOpenAccordions}
          handleRollbackClick={handleRollbackClick}
        />
      ));
  }, [
    messages, 
    activeReplyTo, 
    fileOperationsMap, 
    fileOpsUpdateCounter, 
    isPanelExpanded, 
    setActiveReplyTo, 
    setIsInputEnabled, 
    openAccordions, 
    setOpenAccordions, 
    handleRollbackClick, 
    scrollToMessage, 
    getUserAvatar, 
    getKaviaAvatar, 
    userId, 
    formatDateTime, 
    renderHTML, 
    deploymentStatusMap, 
    textAreaRef
  ]);

  // Auto-scroll when messages change
  useEffect(() => {
    if (messages.length > 0 && autoScroll && isAtBottom()) {
      requestAnimationFrame(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      });
    }
  }, [messages, autoScroll, isAtBottom, messagesContainerRef]);

  // Focus event handlers
  const handleInputFocus = useCallback(() => {
    setInputFocused(true);
  }, []);

  const handleInputBlur = useCallback(() => {
    setInputFocused(false);
  }, []);

  // Comprehensive component cleanup
  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
    };
  }, []);

  // RENDER
  return (
    <>
      <div className="flex flex-col h-screen overflow-hidden">
        {!messages.some(msg => msg.msg_type === "llm") ? (
          taskStatus.toLowerCase() === "failed" ? (
            <ChatLoader message={"Failed to prepare environment. Please try again."} />
          ) : (
            <ChatLoader message={
              messages.filter(msg => msg.msg_type?.toLowerCase() === "system").length > 0
                ? messages.filter(msg => msg.msg_type?.toLowerCase() === "system")
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0].content
                : "Preparing environment..."
            } />
          )
        ) : (
          <>
            {/* Messages container */}
            <div
              ref={messagesContainerRef}
              className={`flex-1 overflow-y-auto p-4 pl-0 custom-scrollbar messages-container relative`}
              onScroll={(e) => handleScroll(e, autoScroll)}
              style={{ scrollBehavior: 'auto', position: 'relative' }}
            >
              <div className="messages-wrapper">
                {renderMessages}
              </div>
            </div>

            {/* AI Typing indicator */}
            {isAiTyping && <ThreeDotLoader />}

            {/* Rollback Progress Bar */}
            {isRollbackInProgress && <RollbackProgressBar />}

            {/* New messages indicator */}
            <NewMessageIndicator 
              hasNewMessages={hasNewMessages}
              messagesContainerRef={messagesContainerRef}
              setAutoScroll={setAutoScroll}
              setHasNewMessages={setHasNewMessages}
            />

            {/* Chat input */}
            <div className="w-full max-w-lg mx-auto pt-1 pb-2 px-4 overflow-x-hidden">
              <ChatInput
                inputValue={inputValue}
                setInputValue={setInputValue}
                handleSendMessage={handleSendMessage}
                isReady={isReady}
                isAiTyping={isAiTyping}
                textAreaRef={textAreaRef}
                activeReplyTo={activeReplyTo}
                availableModels={propAvailableModels}
                isLoadingModels={propIsLoadingModels}
                wsConnection={wsConnection}
                attachedFiles={attachedFiles}
                setAttachedFiles={setAttachedFiles}
                uploadedAttachments={uploadedAttachments}
                setUploadedAttachments={setUploadedAttachments}
                isStopped={isStopped}
                onInputFocus={handleInputFocus}
                onInputBlur={handleInputBlur}
              />
            </div>
          </>
        )}
      </div>

      {/* ScrollToBottomButton */}
      <ScrollToBottomButton 
        autoScroll={autoScroll}
        hasNewMessages={hasNewMessages}
        isAtBottom={isAtBottom}
        inputFocused={inputFocused}
        messagesContainerRef={messagesContainerRef}
        directScrollToBottom={directScrollToBottom}
        setAutoScroll={setAutoScroll}
        setHasNewMessages={setHasNewMessages}
        userManuallyScrolledRef={userManuallyScrolledRef}
      />
      
      {/* Rollback Confirmation Modal */}
      <ConfirmationModal
        showModal={showRollbackModal}
        functionCallData={{
          operation: "rollback",
          type: "Codebase Changes",
          details: {
            "Action": "Rollback to message point",
            "Message ID": rollbackMessageId || "",
            "Description": "This will stash all current changes in the codebase and rollback to the state before this message was processed."
          },
          task_id: searchParams.get("task_id")
        }}
        onCancel={handleRollbackCancel}
        onConfirm={handleRollbackConfirm}
      />
    </>
  );
};

export default ChatInterface; 