// components/MarkdownEditor.tsx

import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import { X } from 'lucide-react';
import 'react-quill/dist/quill.snow.css';
import { renderHTML } from '@/utils/helpers';

const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>
});

const MarkdownEditor = ({
  isOpen,
  onClose,
  content,
  onSave,
  title = 'Edit Content'
}) => {
  const [editContent, setEditContent] = useState(content || '');
  const [isSaving, setIsSaving] = useState(false);

  // Update editContent when content prop changes
  React.useEffect(() => {
    if (isOpen && content !== undefined) {
      // Convert markdown to HTML for editing in ReactQuill
      const htmlContent = renderHTML(content || '');
      setEditContent(htmlContent || '');
    }
  }, [isOpen, content]);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ]
  };

  // Convert HTML back to markdown format
  const htmlToMarkdown = (html) => {
    if (!html || typeof html !== 'string') return '';

    // Create a temporary DOM element to parse HTML properly
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        const children = Array.from(node.childNodes).map(processNode).join('');

        switch (tagName) {
          case 'h1': return `# ${children}\n`;
          case 'h2': return `## ${children}\n`;
          case 'h3': return `### ${children}\n`;
          case 'h4': return `#### ${children}\n`;
          case 'h5': return `##### ${children}\n`;
          case 'h6': return `###### ${children}\n`;
          case 'strong':
          case 'b':
            return `**${children}**`;
          case 'em':
          case 'i':
            return `*${children}*`;
          case 'a':
            const href = node.getAttribute('href') || '';
            return `[${children}](${href})`;
          case 'ul':
            return processListItems(node, false);
          case 'ol':
            return processListItems(node, true);
          case 'li':
            return children; // Will be handled by parent ul/ol
          case 'p':
            return `${children}\n\n`;
          case 'br':
            return '\n';
          default:
            return children;
        }
      }

      return '';
    };

    const processListItems = (listNode, isOrdered) => {
      const items = Array.from(listNode.children).filter(child => child.tagName.toLowerCase() === 'li');
      const listItems = items.map((item, index) => {
        const content = Array.from(item.childNodes).map(processNode).join('').trim();
        const prefix = isOrdered ? `${index + 1}. ` : '- ';
        return prefix + content;
      });
      return listItems.join('\n') + '\n';
    };

    const result = processNode(tempDiv);

    // Clean up extra whitespace and line breaks
    return result
      .replace(/\n{3,}/g, '\n\n')
      .replace(/^\s+|\s+$/g, '')
      .trim();
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Convert HTML content back to markdown before saving
      const markdownContent = htmlToMarkdown(editContent);
      await onSave(markdownContent);
      onClose();
    } catch (error) {

    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="typography-body-lg font-weight-semibold">{title}</h3>
          <button
            onClick={onClose}
            className="p-1.5 hover:bg-gray-100 rounded"
          >
            <X size={16} />
          </button>
        </div>

        <div className="p-4">
          <ReactQuill
            theme="snow"
            value={editContent}
            onChange={setEditContent}
            modules={modules}
            className="h-[400px] mb-12"
          />
        </div>

        <div className="p-4 border-t flex justify-between items-center">
          <div className="typography-caption text-gray-500">
            <span className="mr-4">Ctrl/⌘ + B Bold</span>
            <span className="mr-4">Ctrl/⌘ + I Italic</span>
            <span>Ctrl/⌘ + K Link</span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 border rounded"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownEditor;