// components/MarkdownEditor.tsx

import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import { X } from 'lucide-react';
import 'react-quill/dist/quill.snow.css';
import { renderHTML } from '@/utils/helpers';

const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>
});

const MarkdownEditor = ({
  isOpen,
  onClose,
  content,
  onSave,
  title = 'Edit Content'
}) => {
  const [editContent, setEditContent] = useState(content || '');
  const [isSaving, setIsSaving] = useState(false);

  // Update editContent when content prop changes
  React.useEffect(() => {
    if (isOpen && content !== undefined) {
      // Convert markdown to HTML for editing in ReactQuill
      const htmlContent = renderHTML(content || '');
      setEditContent(htmlContent || '');
    }
  }, [isOpen, content]);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ]
  };

  // Convert HTML back to markdown format
  const htmlToMarkdown = (html) => {
    if (!html || typeof html !== 'string') return '';

    let markdown = html;

    // Convert headers
    markdown = markdown.replace(/<h([1-6])>(.*?)<\/h[1-6]>/g, (match, level, text) => {
      return '#'.repeat(parseInt(level)) + ' ' + text + '\n';
    });

    // Convert bold text
    markdown = markdown.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
    markdown = markdown.replace(/<b>(.*?)<\/b>/g, '**$1**');

    // Convert italic text
    markdown = markdown.replace(/<em>(.*?)<\/em>/g, '*$1*');
    markdown = markdown.replace(/<i>(.*?)<\/i>/g, '*$1*');

    // Convert unordered lists
    markdown = markdown.replace(/<ul>(.*?)<\/ul>/gs, (match, content) => {
      const listItems = content.match(/<li>(.*?)<\/li>/g) || [];
      return listItems.map(item => {
        const text = item.replace(/<li>(.*?)<\/li>/, '$1').trim();
        return '- ' + text;
      }).join('\n') + '\n';
    });

    // Convert ordered lists
    markdown = markdown.replace(/<ol>(.*?)<\/ol>/gs, (match, content) => {
      const listItems = content.match(/<li>(.*?)<\/li>/g) || [];
      return listItems.map((item, index) => {
        const text = item.replace(/<li>(.*?)<\/li>/, '$1').trim();
        return `${index + 1}. ${text}`;
      }).join('\n') + '\n';
    });

    // Convert links
    markdown = markdown.replace(/<a href="(.*?)">(.*?)<\/a>/g, '[$2]($1)');

    // Convert paragraphs
    markdown = markdown.replace(/<p>(.*?)<\/p>/g, '$1\n\n');

    // Clean up extra whitespace and line breaks
    markdown = markdown.replace(/<br\s*\/?>/g, '\n');
    markdown = markdown.replace(/&nbsp;/g, ' ');
    markdown = markdown.replace(/\n{3,}/g, '\n\n');
    markdown = markdown.trim();

    return markdown;
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Convert HTML content back to markdown before saving
      const markdownContent = htmlToMarkdown(editContent);
      await onSave(markdownContent);
      onClose();
    } catch (error) {

    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="typography-body-lg font-weight-semibold">{title}</h3>
          <button
            onClick={onClose}
            className="p-1.5 hover:bg-gray-100 rounded"
          >
            <X size={16} />
          </button>
        </div>

        <div className="p-4">
          <ReactQuill
            theme="snow"
            value={editContent}
            onChange={setEditContent}
            modules={modules}
            className="h-[400px] mb-12"
          />
        </div>

        <div className="p-4 border-t flex justify-between items-center">
          <div className="typography-caption text-gray-500">
            <span className="mr-4">Ctrl/⌘ + B Bold</span>
            <span className="mr-4">Ctrl/⌘ + I Italic</span>
            <span>Ctrl/⌘ + K Link</span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 border rounded"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownEditor;