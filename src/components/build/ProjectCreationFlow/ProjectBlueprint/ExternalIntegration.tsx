import React, { useEffect, useState } from 'react';
import { Link2, Chevron<PERSON>ef<PERSON>, ChevronRight, ChevronDown } from 'lucide-react';
import StripeConnectionModal from './StripeConnectionModal';
import SupabaseConnectionModal from './SupabaseConnectionModal';
import { connectToSupabase } from '@/utils/api';
import { getGitHubConfigurations } from '@/utils/scmAPI';
import { GitHubLogo,SupabaseLogo, StripeLogo } from './ExternalIntegrationLogos';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'not_connected';
  color: {
    bg?: string;
    text: string;
    border: string;
    lightBg?: string;
  };
}



const ExternalIntegrations: React.FC = () => {
  const [stripeModalOpen, setStripeModalOpen] = useState(false);
  const [supabaseModalOpen, setSupabaseModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedGithubOrg, setSelectedGithubOrg] = useState<any>(null);
  // GitHub specific states

  const [showGithubDropdown, setShowGithubDropdown] = useState(false);
  const [githubOrganizations, setGithubOrganizations] = useState([]);
  const [loadingGithubOrgs, setLoadingGithubOrgs] = useState(false);

  // Integration status for non-GitHub integrations
  const [integrationStatus, setIntegrationStatus] = useState<Record<string, 'connected' | 'not_connected'>>({
    supabase: 'not_connected',
    stripe: 'not_connected'
  });

    // Initialize session storage on mount
    useEffect(() => {
      sessionStorage.setItem('selected_github_id', '');
    }, []);

  const checkSupabaseConnection = async () => {
    try {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;
      
      const response = await connectToSupabase(projectId.toString());
      if (response && response.status === "already_connected") {
        setIntegrationStatus(prev => ({
          ...prev,
          supabase: 'connected'
        }));
      }
    } catch (error) {
      console.error("Error checking supabase connection:", error);
    }
  };

  const loadGitHubOrganizations = async () => {
    setLoadingGithubOrgs(true);
    try {
      const response: any = await getGitHubConfigurations();
      if (response && response.data && response.data.configurations) {
        setGithubOrganizations(response.data.configurations);
      }
    } catch (error) {
      console.error("Error loading GitHub organizations:", error);
    } finally {
      setLoadingGithubOrgs(false);
    }
  };

  const integrations: Integration[] = [
    {
      id: 'github',
      name: 'GitHub',
      description: 'Code Repository & Version Control',
      icon: <GitHubLogo />,
      status: selectedGithubOrg ? 'connected' : 'not_connected',
      color: {
        bg: 'bg-gray-900',
        text: 'text-gray-900',
        border: 'border-gray-900',
        lightBg: 'bg-gray-50'
      }
    },
    {
      id: 'supabase',
      name: 'Supabase',
      description: 'Database & Authentication',
      icon: <SupabaseLogo />,
      status: integrationStatus.supabase,
      color: {
        text: 'text-green-500',
        border: 'border-green-500',
        lightBg: 'bg-green-50'
      }
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Payments & Subscriptions',
      icon: <StripeLogo />,
      status: integrationStatus.stripe,
      color: {
        bg: 'bg-purple-600',
        text: 'text-purple-600',
        border: 'border-purple-600',
        lightBg: 'bg-purple-50'
      }
    }
  ];

  // Pagination settings
  const itemsPerPage = 2;
  const totalPages = Math.ceil(integrations.length / itemsPerPage);
  const showCarouselControls = integrations.length > itemsPerPage;

  // Get current page items
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentIntegrations = integrations.slice(startIndex, endIndex);

  const handleConnect = (integrationId: string) => {
    if (integrationId === 'stripe') {
      setStripeModalOpen(true);
    } else if (integrationId === 'supabase') {
      setSupabaseModalOpen(true);
    } else if (integrationId === 'github') {
      setShowGithubDropdown(true);
      // Organizations already loaded on mount, no need to reload
    }
  };

  const handleDisconnect = (integrationId: string) => {
    if (integrationId === 'github') {
      setSelectedGithubOrg(null);
      setShowGithubDropdown(false);
    } else {
      setIntegrationStatus(prev => ({
        ...prev,
        [integrationId]: 'not_connected'
      }));
    }
  };

  const handleStripeConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      stripe: 'connected'
    }));
  };

  const handleSupabaseConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      supabase: 'connected'
    }));
    setSupabaseModalOpen(false);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleSupabaseModalClose = () => {
    setSupabaseModalOpen(false);
    setTimeout(() => {
      checkSupabaseConnection();
    }, 100);
  };

  // Handle GitHub organization selection
  const handleGithubOrgChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    if (selectedId) {
      sessionStorage.setItem("selected_github_id", selectedId)
      const selected = githubOrganizations.find(
        (org: any) => {

          return org.encrypted_scm_id === selectedId
          
        }
      );
      setSelectedGithubOrg(selected || null);
    } else {
      setSelectedGithubOrg(null);
    }
  };

  useEffect(() => {
    checkSupabaseConnection();
    // Load GitHub organizations on component mount
    loadGitHubOrganizations();
  }, []);

  return (
    <>
      <div className="mb-6">
        <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">
          Third party Integrations
        </h3>

        <div className="relative">
          {/* Carousel Container */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentIntegrations.map((integration) => {
              const isConnected = integration.status === 'connected';

              return (
                <div
                  key={integration.id}
                  className={`border rounded-lg p-4 bg-white ${
                    isConnected 
                      ? `border-green-500 bg-green-50` 
                      : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div
                      className={`w-10 h-10 rounded-full ${integration.color.bg} flex items-center justify-center flex-shrink-0`}
                    >
                      {integration.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h4 className="typography-body-sm font-weight-medium text-gray-900">
                        {integration.name}
                      </h4>
                      <p className="typography-caption text-gray-500 mt-0.5">
                        {integration.description}
                      </p>

                      {/* Status */}
                      <div className="flex items-center mt-2">
                        <span className={`w-2 h-2 rounded-full mr-2 ${
                          isConnected ? 'bg-green-500' : 'bg-gray-300'
                        }`}></span>
                        <span className={`typography-caption ${
                          isConnected ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {isConnected ? 'Connected' : 'Not Connected'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* GitHub Special Handling */}
                  {integration.id === 'github' ? (
                    <div className="mt-4">
                      {showGithubDropdown || selectedGithubOrg ? (
                        <div className="space-y-2">
                          {/* GitHub Dropdown */}
                          <div className="relative">
                            <select
                              value={selectedGithubOrg?.encrypted_scm_id || ''}
                              onChange={handleGithubOrgChange}
                              disabled={loadingGithubOrgs}
                              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white ${
                                loadingGithubOrgs ? 'cursor-not-allowed opacity-60' : ''
                              }`}
                            >
                              <option value="">
                                {loadingGithubOrgs ? 'Loading Github' : 'Select GitHub Account'}
                              </option>
                              {!loadingGithubOrgs && githubOrganizations.map((org: any) => (
                                <option key={org.encrypted_scm_id} value={org.encrypted_scm_id}>
                                  {org.organization || org.credentials?.organization}
                                </option>
                              ))}
                            </select>
                            
                            {/* Loader or Chevron */}
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                              {loadingGithubOrgs ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                              ) : (
                                <ChevronDown size={16} className="text-gray-400" />
                              )}
                            </div>
                          </div>
                          
                          {/* Selected Organization Display */}
                          {selectedGithubOrg && (
                            <div className="text-xs text-green-600 flex items-center">
                              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                              Selected: {selectedGithubOrg.organization || selectedGithubOrg.credentials?.organization}
                            </div>
                          )}

                          {/* Disconnect Button for GitHub */}
                          {selectedGithubOrg && (
                            <button
                              onClick={() => handleDisconnect('github')}
                              className="w-full px-4 py-2 border border-gray-300 text-gray-600 bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200"
                            >
                              <span className="typography-body-sm font-weight-medium">
                                Disconnect
                              </span>
                            </button>
                          )}
                        </div>
                      ) : (
                        <button
                          onClick={() => handleConnect(integration.id)}
                          disabled={loadingGithubOrgs}
                          className={`w-full px-4 py-2 border ${integration.color.border} ${integration.color.text} bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 ${
                            loadingGithubOrgs ? 'cursor-not-allowed opacity-60' : ''
                          }`}
                        >
                          {loadingGithubOrgs ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                              <span className="typography-body-sm font-weight-medium">
                                Loading...
                              </span>
                            </>
                          ) : (
                            <>
                              <Link2 size={16} className="mr-2" />
                              <span className="typography-body-sm font-weight-medium">
                                Connect / Select
                              </span>
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  ) : (
                    /* Other Integrations Connect/Disconnect */
                    isConnected ? (
                      <button
                        onClick={() => handleDisconnect(integration.id)}
                        className="mt-4 w-full px-4 py-2 border border-gray-300 text-gray-600 bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200"
                      >
                        <span className="typography-body-sm font-weight-medium">
                          Disconnect
                        </span>
                      </button>
                    ) : (
                      <button
                        onClick={() => handleConnect(integration.id)}
                        className={`mt-4 w-full px-4 py-2 border ${integration.color.border} ${integration.color.text} bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200`}
                      >
                        <Link2 size={16} className="mr-2" />
                        <span className="typography-body-sm font-weight-medium">
                          Connect
                        </span>
                      </button>
                    )
                  )}
                </div>
              );
            })}
          </div>

          {/* Floating Arrow Controls */}
          {showCarouselControls && (
            <>
              {/* Left Arrow */}
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${
                  currentPage === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft size={20} className="text-gray-600" />
              </button>

              {/* Right Arrow */}
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${
                  currentPage === totalPages - 1
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                }`}
                aria-label="Next page"
              >
                <ChevronRight size={20} className="text-gray-600" />
              </button>
            </>
          )}
        </div>

        {/* Dot Indicators */}
        {showCarouselControls && (
          <div className="flex justify-center items-center space-x-2 mt-4">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentPage
                    ? 'bg-gray-800 w-6'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Stripe Connection Modal */}
      <StripeConnectionModal
        isOpen={stripeModalOpen}
        onClose={() => setStripeModalOpen(false)}
        onConnectionComplete={handleStripeConnectionComplete}
      />

      {/* Supabase Connection Modal */}
      <SupabaseConnectionModal
        isOpen={supabaseModalOpen}
        onClose={handleSupabaseModalClose}
        onConnectionComplete={handleSupabaseConnectionComplete}
      />
    </>
  );
};

export default ExternalIntegrations;