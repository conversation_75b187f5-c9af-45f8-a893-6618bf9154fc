"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams, usePathname } from "next/navigation";
import { Info, RefreshCcw, X } from 'lucide-react';
import Sessions from "@/components/Sessions/Sessions";
import { getPastCodeTasks } from "@/utils/batchAPI";
import Pagination from "@/components/UIComponents/Paginations/Pagination";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import dynamic from 'next/dynamic';
import ContainerList from "@/components/BrowsePanel/Architecture/ContainerList"
import dayjs from 'dayjs';
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useUser } from "@/components/Context/UserContext";
import { transformSessionsResponse } from "@/utils/sessionUtils";
const MaintenancePage = dynamic(() => import('./maintenance/page'));
const CodeGenerationInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-orange-50 rounded-md p-2 border-l-2 border-orange-400">
      <div className="text-orange-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">When to use Code Generation</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Generate new code components based on design specifications. Select a container and start the process
        </i>
      </div>
    </div>
  );
};

const CodePage = () => {
  const { is_having_permission } = useUser();
  const { isVisible } = useCodeGeneration();
  const { projectId } = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [sessions, setSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('generation');
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    type: null,
    status: null,
    created_at: null
  });

  const fetchTasks = useCallback(async (page = 1, pageSize = 10, filters = {}) => {
    try {
      setIsLoading(true);
      const skip = (page - 1) * pageSize;

      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.type) queryParams.append('type', filters.type);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.created_at) {
        const formattedDate = dayjs(filters.created_at).format('YYYY-MM-DD');
        queryParams.append('created_at', formattedDate);
      }

      const tasks = await getPastCodeTasks(
        projectId,
        pageSize,
        skip,
        queryParams.toString()
      );

      const transformedData = transformSessionsResponse(tasks);
      setSessions(transformedData.sessions);
      setPagination(prev => ({
        ...prev,
        totalItems: transformedData.pagination.total_count
      }));
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handlePageSizeChange = (newSize) => {
    setPagination(prev => ({
      ...prev,
      pageSize: newSize,
      currentPage: 1
    }));
  };

  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  const handleHistoryOpen = () => {
    fetchTasks(pagination.currentPage, pagination.pageSize, filters);
    setIsHistoryOpen(true);
  };

  useEffect(() => {
    if (searchParams.get('history') === 'true') {
      handleHistoryOpen();
    }
  }, [searchParams]);

  useEffect(() => {
    if (!isHistoryOpen && searchParams.get('history') === 'true') {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('history');
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [isHistoryOpen]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
      // Remove the tab parameter from URL after setting the active tab
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('tab');
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, pathname, router]);

  useEffect(() => {
    fetchTasks(pagination.currentPage, pagination.pageSize, filters);
  }, [projectId, pagination.currentPage, pagination.pageSize, filters]);

  return (
    <div className="overflow-y-auto custom-scrollbar max-h-[78vh]">
      <div className="p-2">
        {/* Header Section with Tabs */}
        <div className="flex flex-col gap-2 mb-4">
          <div className="flex items-center justify-between">
            <div className="bg-gray-100 h-8 flex w-full max-w-[320px] border border-gray-200 rounded-md overflow-hidden">
              <BootstrapTooltip
                title={!is_having_permission() ? "You don't have permission" : "Start code generation"}
                placement="bottom"
              >
                <button
                  onClick={() => {
                    if (is_having_permission()) {
                      setActiveTab('generation');
                    }
                  }}
                  className={`flex-1 px-2 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out ${
                    activeTab === 'generation'
                      ? 'bg-white text-primary-600 shadow-sm border-r border-gray-200'
                      : 'text-gray-600 hover:bg-primary-50 hover:text-primary-600'
                  }`}
                  disabled={!is_having_permission()}
                >
                  Code Generation
                </button>
              </BootstrapTooltip>

              <BootstrapTooltip
                title={!is_having_permission() ? "You don't have permission" : "Start code maintenance"}
                placement="bottom"
              >
                <button
                  onClick={() => {
                    if (is_having_permission()) {
                      setActiveTab('maintenance');
                    }
                  }}
                  className={`flex-1 px-2 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out ${
                    activeTab === 'maintenance'
                      ? 'bg-white text-orange-600 shadow-sm border-l border-gray-200'
                      : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
                  }`}
                  disabled={!is_having_permission()}
                >
                  Code Maintenance
                </button>
              </BootstrapTooltip>
            </div>

            {/* History Button - only show for generation tab */}
            {activeTab === 'generation' && (
              <BootstrapTooltip title={TOOLTIP_CONTENT.codeMaintenance.History} placement="bottom">
                <button
                  onClick={handleHistoryOpen}
                  className="flex items-center justify-center px-3 py-1.5
                  bg-white border border-gray-300 text-gray-700 rounded-md
                  hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600
                  focus:outline-none focus:ring-2 focus:ring-orange-500
                  typography-caption font-weight-medium transition-all duration-200"
                >
                  <RefreshCcw className="mr-1.5 w-3.5 h-3.5" />
                  <span>History</span>
                </button>
              </BootstrapTooltip>
            )}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'generation' && (
          <div>
            <CodeGenerationInfo/>
            <div className="mt-4">
              <ContainerList isModal={true} />
            </div>
          </div>
        )}
        {activeTab === 'maintenance' && (
          <div>
            <MaintenancePage />
          </div>
        )}
      </div>

      {/* History Modal */}
      {isHistoryOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg w-[94vw] h-[94vh] flex flex-col shadow-xl">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <div>
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">Session History</h2>
                <p className="typography-caption text-gray-500 mt-0.5">View and manage your code sessions</p>
              </div>
              <button
                onClick={() => setIsHistoryOpen(false)}
                className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
                aria-label="Close modal"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
                <Sessions
                  initialSessions={sessions}
                  isLoading={isLoading}
                  onFilterChange={handleFilterChange}
                  onCloseModal={() => setIsHistoryOpen(false)}
                  compactMode={true}
                />
              </div>
              <div className="sticky bottom-0 bg-white border-t border-gray-200 py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="typography-caption text-gray-500">
                    Showing {sessions.length} of {pagination.totalItems} sessions
                  </div>
                  <Pagination
                    currentPage={pagination.currentPage}
                    pageCount={Math.max(1, Math.ceil(pagination.totalItems / pagination.pageSize))}
                    pageSize={pagination.pageSize}
                    totalItems={pagination.totalItems}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    pageSizeOptions={[5, 10, 20, 50]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {isVisible && <CodeGenerationModal />}
    </div>
  );
};

export default CodePage;
