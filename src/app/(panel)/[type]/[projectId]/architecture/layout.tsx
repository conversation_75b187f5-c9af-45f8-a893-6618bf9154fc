"use client";

import React, { useState, useEffect, ReactNode, useContext } from "react";
import { useRouter, usePathname } from "next/navigation";
import { FaProjectDiagram, FaTasks, FaComments } from "react-icons/fa";
import { IconType } from "react-icons";
import ConfigureModal from "@/components/Modal/ConfigureModel";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { StateContext } from "@/components/Context/StateContext";
import { TabBar } from "@/components/UIComponents/Tabs/SecondaryTabs/SecondaryTabBar";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { Plus, RefreshCw } from "lucide-react";
import { useUser } from "@/components/Context/UserContext";
import { ExecutionContext } from "@/components/Context/ExecutionContext";
import ReConfigureModal from "@/components/Modal/ReconfigureModel";
import "@/styles/home.css";
import "@/styles/components/StickyActionButtons.css";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";

// Tab definitions
interface Tab {
  id: number;
  name: string;
  label: string;
  icon: IconType;
  tooltip?: string;
}

const subTabs: Tab[] = [
  { id: 1, name: "architecture-requirement", label: "Requirements", icon: FaProjectDiagram, tooltip: TOOLTIP_CONTENT.Architecture.tabs.architecture_requirement },
  { id: 2, name: "system-context", label: "System Context", icon: FaTasks, tooltip: TOOLTIP_CONTENT.Architecture.tabs.system_context },
  { id: 3, name: "container", label: "Containers", icon: FaTasks, tooltip: TOOLTIP_CONTENT.Architecture.tabs.container },
  { id: 4, name: "component", label: "Components", icon: FaTasks, tooltip: TOOLTIP_CONTENT.Architecture.tabs.component },
  { id: 5, name: "design", label: "Designs", icon: FaComments, tooltip: TOOLTIP_CONTENT.Architecture.tabs.design },
  { id: 6, name: "interfaces", label: "Interfaces", icon: FaProjectDiagram, tooltip: TOOLTIP_CONTENT.Architecture.tabs.interfaces },
  { id: 7, name: "software-architecture", label: "SAD", icon: FaTasks, tooltip: TOOLTIP_CONTENT.Architecture.tabs.software_architecture },
  { id: 8, name: "prd", label: "PRD", icon: FaTasks, tooltip: TOOLTIP_CONTENT.Architecture.tabs.prd },
  { id: 9, name: "api-docs", label: "API Docs", icon: FaComments, tooltip: TOOLTIP_CONTENT.Architecture.tabs.api_docs }
];

interface ArchitectureLayoutProps {
  children: ReactNode;
}

const ArchitectureLayout: React.FC<ArchitectureLayoutProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { is_admin, tenant_id, fetchUserData } = useUser();

  const pathParts = pathname.split("/");
  const projectId = pathParts[2];
  const currentTabName = pathParts[4];

  const [activeTab, setActiveTab] = useState<string>(currentTabName || subTabs[0].name);
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingReConfigure, setLoadingReconfigure] = useState(false);
  const [reconfigureModel, setReconfigureModel] = useState(false);

  const { architectureReconfigEnable } = useContext(ExecutionContext);
  const { showAlert } = useContext(AlertContext);
  const { setIsVertCollapse } = useContext(StateContext);

  // Ensure user data is present
  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  // Update tab state from route
  useEffect(() => {
    if (currentTabName) {
      setActiveTab(currentTabName);
    }
  }, [currentTabName]);

  const handleTabClick = (tabName: string) => {
    setActiveTab(tabName);
    sessionStorage.removeItem("querySet");
    router.push(`/project/${projectId}/architecture/${tabName}`);
  };

  const handleConfigureClick = () => setConfigureModel(true);
  const handleReconfigureClick = () => setReconfigureModel(true);

  return (
    <div className="relative architecture-layout">
      {/* Tabs and Actions */}
      <div className="flex items-center justify-between m-2 gap-4">
        {/* Tabs with Navigation */}
        <div className="flex-1 min-w-0">
          <TabBar tabs={subTabs} activeTab={activeTab} onTabClick={handleTabClick} />
        </div>

        {/* Compact Icon-Only Action Buttons */}
        <div className="flex gap-2 items-center ml-auto">
          <BootstrapTooltip title="Auto Configure Architecture" placement="bottom">
            <button
              onClick={handleConfigureClick}
              className="flex items-center justify-center w-8 h-8 bg-orange-500 hover:bg-orange-600 text-white rounded-md transition-colors duration-200"
            >
              <Plus className="w-4 h-4" />
            </button>
          </BootstrapTooltip>

          {architectureReconfigEnable && (
            <BootstrapTooltip title="Re-Configure Architecture" placement="bottom">
              <button
                onClick={handleReconfigureClick}
                disabled={loadingReConfigure}
                className={`flex items-center justify-center w-8 h-8 rounded-md transition-colors duration-200 ${
                  loadingReConfigure
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-primary-500 hover:bg-primary-600 text-white'
                }`}
              >
                <RefreshCw className={`w-4 h-4 ${loadingReConfigure ? 'animate-spin' : ''}`} />
              </button>
            </BootstrapTooltip>
            )}
        </div>
      </div>

      {/* Content */}
      <div>{children}</div>

      {/* Modals */}
      {configureModel && (
        <ConfigureModal
          id={projectId}
          isNodeType="Architecture"
          type="Architecture"
          closeModal={() => setConfigureModel(false)}
          setLoadingAutoConfigure={() => {}}
          onSubmitSuccess={() => {
            showAlert("Architecture Configured Successfully", "success");
            setIsVertCollapse(false);
          }}
        />
      )}

      {reconfigureModel && (
        <ReConfigureModal
          id={projectId}
          isNodeType="Architecture"
          type="Architecture"
          closeModal={() => setReconfigureModel(false)}
          setLoadingAutoConfigure={setLoadingReconfigure}
          onSubmitSuccess={() => {
            showAlert("Architecture Re-Configured Successfully", "success");
            setIsVertCollapse(false);
          }}
        />
      )}
    </div>
  );
};

export default ArchitectureLayout;
