"use client";

import React, { useState, useEffect, useContext } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ChevronLeft, ChevronRight, Plus, Trash2, Folder, ChevronDown, Code } from "lucide-react";
import { getTestCaseCategories, getTestCasesByCategory } from "@/utils/testcaseApi";
import { getTestCasesByNodeType, getAutomatableTestCases, exportTestCasesToExcel } from "@/utils/testcaseApi";
import { useUser } from "@/components/Context/UserContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { startTestCodeGeneration } from "@/utils/api";

const Page = () => {
  const router = useRouter();
  const pathname = usePathname();
  const projectId = pathname?.split("/")[2] || "";

  const { is_admin, tenant_id, fetchUserData } = useUser();
  const { showAlert } = useContext(AlertContext);
  const [isGeneratingTestcase, setIsGeneratingTestcase] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState(projectId);

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  // State variables
  const [isOpen, setIsOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [testSuites, setTestSuites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [testCasesLoading, setTestCasesLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTestSuite, setSelectedTestSuite] = useState(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [testCases, setTestCases] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [selectedTestCases, setSelectedTestCases] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);

        const response = await getTestCaseCategories(projectId);

        if (!response || Object.keys(response).length === 0) {

          setTestSuites([]);
          setLoading(false);
        } else {
          processCategories(response);
        }
      } catch (err) {
        setLoading(false);
      }
    };

    // Process and format the categories data
    const processCategories = (response) => {
      try {
        // Format categories as test suites with parent-child structure
        let formattedSuites = [];

        // Initialize expanded state for each category
        const initialExpandedState = {};

        // Process all fields in the response
        Object.keys(response).forEach((categoryType) => {
          // Check if this is an object (nested structure)
          if (typeof response[categoryType] === 'object' && response[categoryType] !== null) {
            const categoryId = `${categoryType.toLowerCase()}-header`;
            initialExpandedState[categoryId] = false;

            // Create parent category
            const parentCategory = {
              id: categoryId,
              name: categoryType,
              isHeader: true,
              children: []
            };

            // Process each sub-category within the nested structure
            Object.keys(response[categoryType]).forEach((subType) => {
              const subTypeObj = response[categoryType][subType];
              const subTypeId = `${categoryType.toLowerCase()}-${subType.toLowerCase()}-header`;
              initialExpandedState[subTypeId] = false;

              // Create sub-category
              const subCategory = {
                id: subTypeId,
                name: subType,
                isSubHeader: true,
                parentId: categoryId,
                children: []
              };

              // Process each test case type within the sub-category
              Object.keys(subTypeObj).forEach((testCaseType) => {
                const testCases = subTypeObj[testCaseType];
                if (Array.isArray(testCases) && testCases.length > 0) {
                  const testCaseTypeId = `${categoryType.toLowerCase()}-${subType.toLowerCase()}-${testCaseType.toLowerCase()}-header`;
                  initialExpandedState[testCaseTypeId] = false;

                  // Create test case type category
                  const testCaseTypeCategory = {
                    id: testCaseTypeId,
                    name: testCaseType.replace('TestCase', ''),
                    isTestCaseType: true,
                    parentId: subTypeId,
                    children: testCases.map((testCase, index) => ({
                      id: `${categoryType.toLowerCase()}-${subType.toLowerCase()}-${testCaseType.toLowerCase()}-${index}`,
                      name: testCase,
                      categoryType: categoryType,
                      subType: subType,
                      testCaseType: testCaseType.replace('TestCase', '')
                    }))
                  };

                  subCategory.children.push(testCaseTypeCategory);
                }
              });

              // Only add the sub-category if it has children
              if (subCategory.children.length > 0) {
                parentCategory.children.push(subCategory);
              }
            });

            // Only add the parent category if it has children
            if (parentCategory.children.length > 0) {
              formattedSuites.push(parentCategory);
            }
          }
        });

        setExpandedCategories(initialExpandedState);
         // Debug log
        setTestSuites(formattedSuites);
        setLoading(false);
        // Clear any previous errors
        setError(null);

        // Select the first category item by default if available
        if (formattedSuites.length > 0 && formattedSuites[0].children.length > 0) {
          const firstSuite = formattedSuites[0];
          const firstSubCategory = firstSuite.children[0];
          if (firstSubCategory.children.length > 0) {
            const firstTestCaseType = firstSubCategory.children[0];
            if (firstTestCaseType.children.length > 0) {
              const firstTestCase = firstTestCaseType.children[0];
              setSelectedTestSuite(firstTestCase);

              // Open the folders containing the selected test case
              setExpandedCategories(prev => ({
                ...prev,
                [firstSuite.id]: true,
                [firstSubCategory.id]: true,
                [firstTestCaseType.id]: true
              }));
            }
          }
        }
      } catch (err) {
        
        setError("Failed to process test suites. Please try again later.");
        setLoading(false);
      }
    };

    if (projectId) {
      fetchCategories();
    }
  }, [projectId]);

  // Toggle category expansion and fetch node type items if needed
  const toggleCategoryExpansion = async (categoryId) => {
    // Find the category being toggled
    const category = testSuites.find(suite => suite.id === categoryId);

    // If it's a node type category and we're opening it for the first time (no children loaded)
    if (category?.isNodeType && !expandedCategories[categoryId] && category.children.length === 0) {
      try {
        setLoading(true);
        // Fetch items for this node type
        const response = await getTestCasesByNodeType(projectId, category.nodeType);


        // Update the category with the fetched items
        const updatedTestSuites = testSuites.map(suite => {
          if (suite.id === categoryId) {
            return {
              ...suite,
              children: response.map((item, index) => ({
                id: `${category.nodeType.toLowerCase()}-${index}`,
                name: item.name || item.title || item.id || `Item ${index + 1}`,
                nodeType: category.nodeType,
                // Include the whole item for direct access to test cases
                nodeItem: item
              }))
            };
          }
          return suite;
        });

        setTestSuites(updatedTestSuites);
        setLoading(false);
      } catch (err) {
        
        setError(`Failed to load ${category.nodeType} items. Please try again later.`);
        setLoading(false);
      }
    }

    // Toggle the expanded state
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Fetch test cases when a category is selected
  useEffect(() => {
    const fetchTestCases = async () => {
      if (!selectedTestSuite) return;

      try {
        setTestCasesLoading(true);
        let response;

        // Check if this is a node type item or a regular category
        if (selectedTestSuite.nodeType && selectedTestSuite.nodeItem) {
          // For node types like UserStory and Component, the test cases are already in the item
          // We just need to extract and format them
          if (Array.isArray(selectedTestSuite.nodeItem.testcases)) {
            response = selectedTestSuite.nodeItem.testcases;
          } else {
            // If for some reason testcases aren't available, set an empty array
            response = [];
          }

        } else if (selectedTestSuite.isNested) {
          // For nested categories (like those under Automation)
          // We may need special handling based on the parent category type
          if (selectedTestSuite.parentType === 'Automation') {
            response = await getAutomatableTestCases(projectId, selectedTestSuite.name, selectedTestSuite.categoryType);

          } else {
            // Generic handler for other nested category types that might be added in the future
            response = await getTestCasesByCategory(projectId, selectedTestSuite.name, selectedTestSuite.parentType);

          }
        } else if (selectedTestSuite.name) {
          // Standard case - fetch test cases by category name
          response = await getTestCasesByCategory(projectId, selectedTestSuite.name);

        } else {
          return;
        }

        // Transform API response to match table structure
        const formattedTestCases = response.map(testCase => ({
          id: testCase.id,
          title: testCase.properties?.Title || testCase.title || 'Untitled Test Case',
          priority: testCase.properties?.Priority || testCase.priority || 'Normal',
          estimate: testCase.properties?.Estimate || testCase.estimate || '-',
          status: testCase.properties?.Status || testCase.status || 'Active',
          type: testCase.properties?.Type || testCase.type || 'Functional',
          category: testCase.properties?.Category || testCase.category,
          description: testCase.properties?.Description || testCase.description,
          canBeAutomated: testCase.properties?.CanBeAutomated || testCase.canBeAutomated || false,
          lastModified: testCase.lastModified || new Date().toISOString().split('T')[0],
          properties: testCase.properties || testCase
        }));

        setTestCases(formattedTestCases);
        setTestCasesLoading(false);
      } catch (err) {
        
        setTestCases([]);
        setTestCasesLoading(false);
      }
    };

    fetchTestCases();
  }, [selectedTestSuite, projectId]);

  // Handle navigation to test case details
  const handleActionPerformed = (id) => {

    // Find the selected test case to get its type
    const selectedTestCase = testCases.find(tc => tc.id === id);
    if (selectedTestCase) {
      // Use the test case's type for navigation, fallback to "category" if not available
      // Only remove spaces but preserve original casing
      const testCaseType = selectedTestCase.type.replace(/\s+/g, '') || "category";
      router.push(`/project/${projectId}/testcase/${id}/${testCaseType}`);
    } else {
      // Fallback to just the ID if test case not found, the redirect page will handle the type
      router.push(`/project/${projectId}/testcase/${id}`);
    }
  };

  // Handle folder selection in the file tree
  const handleTestSuiteSelect = (testSuite) => {
    setSelectedTestSuite(testSuite);
    setCurrentPage(1); // Reset to first page when changing test suite
    setSelectedTestCases([]); // Clear selected test cases
    
    // If the test suite has a container property, set it as the selected container
    if (testSuite && testSuite.container && testSuite.container.id) {
      setSelectedContainer(testSuite.container.id);
    }
  };

  // Handle creating a new test suite
  const handleCreateTestSuite = (newTestSuite) => {
    setTestSuites((prevData) => [...prevData, newTestSuite]);
  };

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Open delete confirmation modal
  const openDeleteModal = () => {
    if (selectedTestCases.length > 0) {
      setDeleteModalOpen(true);
    }
  };

  // Handle delete action from modal
  const handleDelete = () => {
    // Filter out deleted test cases
    setTestCases(prevTestCases =>
      prevTestCases.filter(tc => !selectedTestCases.includes(tc.id))
    );
    setDeleteModalOpen(false);
    setSelectedTestCases([]);
  };

  // Handle selection of a single test case
  const handleTestCaseSelection = (id) => {
    setSelectedTestCases(prev => {
      if (prev.includes(id)) {
        return prev.filter(caseId => caseId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Handle "select all" checkbox
  const handleSelectAll = (event) => {
    if (event.target.checked) {
      // Add all visible test cases to selection if not already selected
      const visibleIds = paginatedTestCases.map(tc => tc.id);
      setSelectedTestCases(prev => {
        const newSelection = [...prev];
        visibleIds.forEach(id => {
          if (!newSelection.includes(id)) {
            newSelection.push(id);
          }
        });
        return newSelection;
      });
    } else {
      // Remove all visible test cases from selection
      const visibleIds = paginatedTestCases.map(tc => tc.id);
      setSelectedTestCases(prev =>
        prev.filter(id => !visibleIds.includes(id))
      );
    }
  };

  // Handle export to Excel
  const handleExportToExcel = async () => {
    try {
      setExporting(true);
      setDropdownOpen(false);
      
      const blob = await exportTestCasesToExcel(projectId);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `test-cases-${projectId}-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Cleanup
      window.URL.revokeObjectURL(url);
      
      showAlert('Test cases exported successfully!', 'success');
    } catch (error) {
      console.error('Export failed:', error);
      showAlert('Failed to export test cases. Please try again.', 'error');
    } finally {
      setExporting(false);
    }
  };

  // Toggle dropdown
  const toggleDropdown = (e) => {
    e.stopPropagation();
    setDropdownOpen(!dropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen(false);
    };

    if (dropdownOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [dropdownOpen]);

  // Pagination handlers
  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleItemsPerPageChange = (event) => {
    setItemsPerPage(Number(event.target.value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Filter test cases based on selected test suite - no need to filter as API already returns filtered cases
  const filteredTestCases = testCases;

  // Calculate pagination values
  const totalPages = Math.max(1, Math.ceil(filteredTestCases.length / itemsPerPage));

  // Ensure current page is valid
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [filteredTestCases.length, itemsPerPage, currentPage, totalPages]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedTestCases = filteredTestCases.slice(indexOfFirstItem, indexOfLastItem);

  // Check if all visible items are selected
  const areAllVisibleSelected = paginatedTestCases.length > 0 &&
    paginatedTestCases.every(tc => selectedTestCases.includes(tc.id));

  // Add handleGenerateTestcase function
  const handleGenerateTestcase = async () => {
    // Use projectId as fallback if no container is selected
    const containerToUse = selectedContainer || projectId;
    
    setIsGeneratingTestcase(true);
    try {
      console.log("Generating testcase for container:", containerToUse);
      const response = await startTestCodeGeneration(projectId, containerToUse);
      
      console.log("Test generation response:", response);
      
      // Check if we have a response object
      if (response) {
        // The API function should now handle the parsing of SSE format
        // But we'll add a fallback just in case
        let taskId = response.task_id;
        
        // If response is a string, try to extract task_id
        if (typeof response === 'string') {
          console.log("Response is a string, attempting to parse");
          try {
            // Try to extract JSON from SSE format
            const dataMatch = response.match(/data: ({.*})/);
            if (dataMatch && dataMatch[1]) {
              const parsed = JSON.parse(dataMatch[1]);
              taskId = parsed.task_id;
              console.log("Extracted task_id from string:", taskId);
            }
          } catch (parseError) {
            console.error("Error extracting task_id from string:", parseError);
          }
        }
        
        if (taskId) {
          console.log("Redirecting with task_id:", taskId);
          showAlert("Test case generation has been successfully initiated", "success");
          router.push(`/${pathname.split('/')[1]}/${projectId}/code?task_id=${taskId}`);
        } else {
          console.error("No task_id found in response:", response);
          throw new Error("No task_id found in response");
        }
      } else {
        throw new Error("No response from test case generation API");
      }
    } catch (error) {
      console.error("Test generation error:", error);
      showAlert("Test case generation failed. Please try again later.", "error");
    } finally {
      setIsGeneratingTestcase(false);
    }
  };

  return (
    <div className="relative flex flex-col h-full">
      {/* Top Header */}
      <div className="border-b border-gray-200">
        <div className="flex justify-between items-center px-4 py-3">
          <div>
            <h1 className="text-lg font-medium">Test Cases</h1>
            <p className="text-sm text-gray-500">
              Manage test cases for functional and regression testing
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button 
              onClick={handleGenerateTestcase}
              disabled={isGeneratingTestcase}
              className={`flex items-center gap-1 px-2 py-1 bg-white border border-gray-200 rounded text-sm ${
                isGeneratingTestcase 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:bg-gray-50'
              }`}
            >
              <Code size={14} />
              Generate Testcases
            </button>
            <div className="text-sm text-gray-500 px-3 py-1 bg-gray-100 rounded">
              Auto Config
            </div>
            <div className="relative">
              <button 
                onClick={toggleDropdown}
                className="text-gray-400 hover:text-gray-600"
                disabled={exporting}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="19" cy="12" r="1" />
                  <circle cx="5" cy="12" r="1" />
                </svg>
              </button>
              
              {/* Dropdown Menu */}
              {dropdownOpen && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <div className="py-1">
                    <button
                      onClick={handleExportToExcel}
                      disabled={exporting}
                      className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {exporting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                          Exporting...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <path d="M12 18v-6"/>
                            <path d="M9 15l3 3 3-3"/>
                          </svg>
                          Export to Excel
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
            <button className="text-gray-400 hover:text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M15 3h6v6M14 10l7-7M9 21H3v-6M10 14l-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area with sidebar and table */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Sidebar */}
        <div className={`border-r border-gray-200 transition-all duration-300 ease-in-out bg-white z-10 ${sidebarCollapsed ? 'w-0 opacity-0' : 'w-64 opacity-100'}`}>
          <div className="p-3 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-sm font-medium">Test Suites</h2>
              <button
                onClick={toggleSidebar}
                className="p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition-opacity duration-300"
                aria-label="Collapse sidebar"
              >
                <ChevronLeft size={16} />
              </button>
            </div>
          </div>
          <div className="overflow-y-auto h-[calc(100vh-200px)] pb-8">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : error ? (
              <div className="p-4 text-center text-red-600 text-sm">
                {error}
              </div>
            ) : testSuites.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                No test suites available
              </div>
            ) : (
              <div className="p-2">
                {testSuites.map((category) => (
                  <div key={category.id} className="mb-2">
                    <div
                      className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
                      onClick={() => toggleCategoryExpansion(category.id)}
                    >
                      <div className="flex items-center">
                        <Folder size={16} className="mr-2 text-gray-500" />
                        <span className="text-sm font-medium">{category.name}</span>
                      </div>
                      <ChevronDown
                        size={16}
                        className={`transition-transform duration-200 ${expandedCategories[category.id] ? 'transform rotate-180' : ''}`}
                      />
                    </div>

                    {expandedCategories[category.id] && (
                      <div className="ml-6 mt-1">
                        {category.children.map((subCategory) => (
                          <div key={subCategory.id}>
                            <div
                              className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
                              onClick={() => toggleCategoryExpansion(subCategory.id)}
                            >
                              <div className="flex items-center">
                                <Folder size={14} className="mr-2 text-gray-400" />
                                <span className="text-sm">{subCategory.name}</span>
                              </div>
                              <ChevronDown
                                size={14}
                                className={`transition-transform duration-200 ${expandedCategories[subCategory.id] ? 'transform rotate-180' : ''}`}
                              />
                            </div>

                            {expandedCategories[subCategory.id] && (
                              <div className="ml-6 mt-1">
                                {subCategory.children.map((testCaseType) => (
                                  <div key={testCaseType.id}>
                                    <div
                                      className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
                                      onClick={() => toggleCategoryExpansion(testCaseType.id)}
                                    >
                                      <div className="flex items-center">
                                        <Folder size={14} className="mr-2 text-gray-400" />
                                        <span className="text-sm">{testCaseType.name}</span>
                                      </div>
                                      <ChevronDown
                                        size={14}
                                        className={`transition-transform duration-200 ${expandedCategories[testCaseType.id] ? 'transform rotate-180' : ''}`}
                                      />
                                    </div>

                                    {expandedCategories[testCaseType.id] && (
                                      <div className="ml-6 mt-1">
                                        {testCaseType.children.map((testCase) => (
                                          <div
                                            key={testCase.id}
                                            onClick={() => handleTestSuiteSelect(testCase)}
                                            className={`flex items-center p-2 rounded text-sm cursor-pointer hover:bg-gray-100 ${selectedTestSuite?.id === testCase.id ? 'bg-blue-50 text-blue-600' : ''}`}
                                          >
                                            <Folder size={12} className="mr-2 text-gray-400" />
                                            <span>{testCase.name}</span>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Outside toggle button - always present but with conditional opacity/visibility */}
        <button
          onClick={toggleSidebar}
          className={`absolute top-3 z-30 bg-white h-8 w-8 flex items-center justify-center rounded-md shadow-md border border-gray-200 transition-all duration-300 ease-in-out ${sidebarCollapsed ? 'opacity-100 left-2' : 'opacity-0 left-64 pointer-events-none'}`}
          aria-label="Expand sidebar"
        >
          <ChevronRight size={16} />
        </button>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Filter and Actions row */}
          <div className="p-3 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                {/* <button
                  className="flex items-center gap-1 px-2 py-1 text-sm rounded hover:bg-gray-100 text-gray-700"
                >
                  Filter
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 4H2l8 9.46V19l4 2v-7.54L22 4z" />
                  </svg>
                </button>
                <button className="p-1 rounded hover:bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="16" />
                    <line x1="8" y1="12" x2="16" y2="12" />
                  </svg>
                </button> */}
              </div>
              <div className="flex gap-2">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search"
                    className="w-64 pl-8 pr-2 py-1 text-sm border border-gray-200 rounded"
                  />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-400 absolute left-2 top-1/2 transform -translate-y-1/2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <button
                  onClick={() => setIsOpen(true)}
                  className="flex items-center gap-1 px-2 py-1 bg-white border border-gray-200 rounded text-sm"
                >
                  <Plus size={14} />
                  Case
                </button>
                <button
                  onClick={openDeleteModal}
                  className={`flex items-center gap-1 px-2 py-1 bg-white border border-gray-200 rounded text-sm ${selectedTestCases.length === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
                  disabled={selectedTestCases.length === 0}
                >
                  <Trash2 size={14} />
                  Delete
                </button>
              </div>
            </div>
          </div>

          {/* Table Area */}
          <div className="flex-1 overflow-auto p-3">
            {/* Custom Table with Checkboxes */}
            <div className="border border-gray-200 rounded-md overflow-hidden">
              {testCasesLoading ? (
                <div className="py-12 flex justify-center items-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
                </div>
              ) : testCases.length === 0 ? (
                <div className="py-12 flex flex-col justify-center items-center text-gray-500">
                  {selectedTestSuite ? (
                    <>
                      <p className="mb-2">No test cases found for this category</p>
                      <button
                        onClick={() => setIsOpen(true)}
                        className="flex items-center gap-1 px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                      >
                        <Plus size={16} />
                        Create Test Case
                      </button>
                    </>
                  ) : (
                    <p>Select a category to view test cases</p>
                  )}
                </div>
              ) : (
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left">
                        <input
                          type="checkbox"
                          onChange={handleSelectAll}
                          checked={areAllVisibleSelected && paginatedTestCases.length > 0}
                          className="rounded border-gray-300"
                        />
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Case</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Can Automate</th>
                      <th className="px-3 py-2"></th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {paginatedTestCases.map((testCase) => (
                      <tr
                        key={testCase.id}
                        className={`hover:bg-gray-50 cursor-pointer ${selectedTestCases.includes(testCase.id) ? 'bg-blue-50' : ''}`}
                        onClick={(e) => {
                          // Don't navigate if clicking on the checkbox
                          if (e.target.type !== 'checkbox') {
                            handleActionPerformed(testCase.id);
                          }
                        }}
                      >
                        <td className="px-3 py-3">
                          <input
                            type="checkbox"
                            checked={selectedTestCases.includes(testCase.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleTestCaseSelection(testCase.id);
                            }}
                            onClick={(e) => e.stopPropagation()}
                            className="rounded border-gray-300"
                          />
                        </td>
                        <td className="px-3 py-3 text-sm text-gray-900">{testCase.title}</td>
                        <td className="px-3 py-3 text-sm text-gray-500">{testCase.type}</td>
                        <td className="px-3 py-3">
                          <span className={`px-2 py-1 inline-flex text-xs font-semibold rounded-md ${testCase.priority === 'High' ? 'bg-red-100 text-red-800' :
                            testCase.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              testCase.priority === 'Normal' ? 'bg-green-100 text-green-800' :
                                'bg-gray-100 text-gray-800'
                            }`}>
                            {testCase.priority}
                          </span>
                        </td>
                        <td className="px-3 py-3 text-sm">
                          <span className={`px-2 py-1 inline-flex text-xs font-semibold rounded-md ${testCase.canBeAutomated ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                            {testCase.canBeAutomated ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td className="px-3 py-3 text-right text-sm font-medium">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                            </svg>
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>

            <div className="flex justify-between items-center mt-4 text-sm text-gray-500">
              <span>
                {filteredTestCases.length > 0
                  ? `${indexOfFirstItem + 1}-${Math.min(indexOfLastItem, filteredTestCases.length)} of ${filteredTestCases.length}`
                  : '0-0 of 0'}
              </span>
              <div className="flex items-center">
                <div className="flex items-center mr-4">
                  <span className="mr-2">Rows per page: </span>
                  <select
                    value={itemsPerPage}
                    onChange={handleItemsPerPageChange}
                    className="border border-gray-200 rounded px-2 py-1 w-16"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>
                <div className="flex items-center h-8">
                  <button
                    className={`w-8 h-8 border border-gray-200 rounded-l flex items-center justify-center ${currentPage === 1 ? 'text-gray-300' : 'text-gray-600'}`}
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <div className="h-8 px-4 border-t border-b border-gray-200 flex items-center justify-center min-w-[60px]">
                    <span>{currentPage}/{totalPages || 1}</span>
                  </div>
                  <button
                    className={`w-8 h-8 border border-gray-200 rounded-r flex items-center justify-center ${currentPage === totalPages || totalPages === 0 ? 'text-gray-300' : 'text-gray-600'}`}
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages || totalPages === 0}
                  >
                    <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-md shadow-lg w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-start mb-2">
                <h2 className="text-xl font-medium">Delete case?</h2>
                <button onClick={() => setDeleteModalOpen(false)} className="text-gray-400 hover:text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>

              <p className="text-gray-600 mb-4">
                Are you sure you want to delete {selectedTestCases.length > 1 ? 'these cases' : 'this case'}?
              </p>

              <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#b45309" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-amber-800 font-medium mb-1">Warning</h3>
                    <p className="text-amber-700 text-sm">
                      All results for {selectedTestCases.length > 1 ? 'these cases' : 'this case'} in active test runs will also be deleted
                      unless you close the runs first.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setDeleteModalOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  </svg>
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Page;