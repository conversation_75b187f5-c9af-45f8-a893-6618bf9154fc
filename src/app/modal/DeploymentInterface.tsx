import React, { useState, useEffect, useCallback } from 'react';
import { X, Rocket, Terminal, Package, Plus, Trash2, Save, ChevronDown, Folder, RefreshCw, Check, History } from 'lucide-react';
import Drawer from '@/components/Drawer';
import { useDeployment } from '@/components/Context/DeploymentContext';
import { listDeployments } from '@/utils/deploymentApi';
import { useParams, useSearchParams } from 'next/navigation';

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url: string): string => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

// Framework logo components
const ReactLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" className="text-[#61DAFB]">
    <circle cx="12" cy="12" r="2.139" fill="currentColor" />
    <path stroke="currentColor" strokeWidth="0.6" d="M12 14.880c-5.03 0-9.268-1.493-9.268-3.315 0-1.823 4.238-3.316 9.268-3.316 5.03 0 9.268 1.493 9.268 3.316 0 1.822-4.238 3.315-9.268 3.315Z" />
    <path stroke="currentColor" strokeWidth="0.6" d="m8.627 12.811.205.356c1.707 2.955 3.56 4.642 4.988 4.025 1.43-.618 1.776-3.153.86-6.302l-.092-.315m-5.755 2.236-.206-.356C7.92 9.5 8.015 6.9 9.443 6.283c1.43-.618 3.513.843 5.22 3.798l.091.158" />
  </svg>
);

const AngularLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2.5L2.5 6l1.5 13L12 22l8-3 1.5-13L12 2.5z" fill="#DD0031" />
    <path d="M12 2.5v19.5l8-3 1.5-13L12 2.5z" fill="#C3002F" />
    <path d="M12 5l-7 15h2.6l1.4-3.5h6l1.4 3.5H18L12 5zm0 4l2.3 5.5H9.7L12 9z" fill="white" />
  </svg>
);

const NextLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path fill="black" d="M11.572 0c-.176 0-.31.001-.358.007a19.76 19.76 0 0 0-.364.033C7.443.346 4.25 2.185 2.228 5.012a11.875 11.875 0 0 0-2.119 5.243c-.096.659-.108.854-.108 1.747s.012 1.089.108 1.748c.652 4.506 3.86 8.292 8.209 9.695.779.25 1.6.422 2.534.525.363.04 1.935.04 2.299 0 1.611-.178 2.977-.577 4.323-1.264.207-.106.247-.134.219-.158-.02-.013-.9-1.193-1.957-2.62l-1.919-2.592-2.404-3.558a338.739 338.739 0 0 0-2.422-3.556c-.009-.002-.018 1.579-.023 3.51-.007 3.38-.01 3.515-.052 3.595a.426.426 0 0 1-.206.214c-.075.037-.14.044-.495.044H7.81l-.108-.068a.438.438 0 0 1-.157-.172l-.05-.106.006-4.703.007-4.705.072-.092a.645.645 0 0 1 .174-.143c.096-.047.134-.051.5-.051.478 0 .558.018.682.154.035.038 1.337 1.999 2.895 4.361a10760.433 10760.433 0 0 0 4.735 7.17l1.9 2.879.096-.063a12.317 12.317 0 0 0 2.466-2.163 11.944 11.944 0 0 0 2.824-6.134c.096-.66.108-.854.108-1.748 0-.893-.012-1.088-.108-1.747-.652-4.506-3.859-8.292-8.208-9.695a12.597 12.597 0 0 0-2.499-.523A33.119 33.119 0 0 0 11.573 0zm4.069 7.217c.347 0 .408.005.486.047a.473.473 0 0 1 .237.277c.018.06.023 1.365.018 4.304l-.006 4.218-.744-1.14-.746-1.14v-3.066c0-1.983.01-3.097.023-3.15a.478.478 0 0 1 .233-.296c.096-.05.13-.054.5-.054z" />
  </svg>
);

const ViteLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12.5 4.3L3.5 7.8l1.7 14.3 7.4 3.9 7.2-4 1.7-14.3-9-3.4z" fill="#646CFF" />
    <path d="M16.3 5L12 9.8 7.7 5h8.6z" fill="#FFDD35" />
    <path d="M16.9 18.4c.9-.9 1.4-2.1 1.4-3.4 0-2.6-2.1-4.7-4.7-4.7-1.3 0-2.5.5-3.4 1.4l6.7 6.7z" fill="white" />
    <path d="M7.1 18.4c-.9-.9-1.4-2.1-1.4-3.4 0-2.6 2.1-4.7 4.7-4.7 1.3 0 2.5.5 3.4 1.4l-6.7 6.7z" fill="white" />
  </svg>
);

const VueLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 4.5L14.5 9h-5L12 4.5z" fill="#41B883" />
    <path d="M12 4.5L9.5 9h-4L12 4.5z" fill="#41B883" />
    <path d="M2 9h3.5L12 22.5 20.5 9H24L12 29 2 9z" fill="#41B883" />
    <path d="M2 9l10 20L22 9h-4.5L12 19 6.5 9H2z" fill="#35495E" />
  </svg>
);

// Backend framework logo components
const FastAPILogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2L14.5 9h7L17 13.5l2.5 7L12 17l-7.5 3.5L7 13.5 2.5 9h7L12 2z" fill="#009485" />
    <circle cx="12" cy="12" r="3" fill="white" />
  </svg>
);

const NodeLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2L22 7v10l-10 5L2 17V7l10-5z" fill="#68A063" />
    <path d="M12 7v10l8-4V9l-8-2z" fill="#8CC84B" />
  </svg>
);

const DjangoLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <rect x="4" y="2" width="4" height="20" rx="2" fill="#092E20" />
    <rect x="10" y="6" width="4" height="16" rx="2" fill="#092E20" />
    <rect x="16" y="4" width="4" height="18" rx="2" fill="#092E20" />
  </svg>
);

const ExpressLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M24 18.588a1.529 1.529 0 01-1.895-.72l-3.45-4.771-.5-.667-4.003 5.444a1.466 1.466 0 01-1.802.708l5.158-6.92-4.798-6.251a1.595 1.595 0 011.9-.666L17.227 10.5l.582.775 3.581-4.96a1.574 1.574 0 011.892.664l-4.645 6.4 4.803 6.2z" fill="#68A063" />
  </svg>
);

const FlaskLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M9 2h6v6l6 12H3l6-12V2z" fill="#000" />
    <path d="M9 8h6l5 10H4l5-10z" fill="#fff" />
  </svg>
);

interface DeploymentInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  selectedContainer?: any;
}

interface EnvVariable {
  key: string;
  value: string;
  id: string;
}

interface PresetConfig {
  name: string;
  command: string;
  installPackages: boolean;
  envVariables: EnvVariable[];
  deploymentPath: string;
}

interface DirContent {
  name: string;
  type: 'file' | 'folder';
}

// Interface for deployment data
interface Deployment {
  _id: string;
  deployment_id: string;
  project_id: string;
  task_id: string;
  app_id: string;
  branch_name: string;
  app_url: string;
  artifact_path: string;
  job_id: string;
  status: string;
  message: string;
  command: string;
  root_path: string;
  build_path: string;
  created_at: string;
  updated_at: string;
  branch?: string;
}

const PLATFORM_PRESETS: Record<string, PresetConfig> = {
  netlify: {
    name: 'Netlify',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'NETLIFY', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: 'dist'
  },
  vercel: {
    name: 'Vercel',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'VERCEL', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: '/'
  },
  githubPages: {
    name: 'GitHub Pages',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'PUBLIC_URL', value: '/${repository_name}', id: 'preset-2' }
    ],
    deploymentPath: 'build'
  },
  cloudflare: {
    name: 'Cloudflare Pages',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'CF_PAGES', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: 'dist'
  }
};

// Framework-specific build commands
const FRAMEWORK_COMMANDS: Record<string, {command: string, description: string, type: 'frontend' | 'backend'}> = {
  react: {
    command: 'npm run build',
    description: 'Create React App',
    type: 'frontend'
  },
  angular: {
    command: 'ng build --prod',
    description: 'Angular CLI',
    type: 'frontend'
  },
  nextjs: {
    command: 'CONFIG_FILE="next.config.ts" && if [ -f "$CONFIG_FILE" ]; then cp "$CONFIG_FILE" "${CONFIG_FILE}.bak" && sed -i \'s/= {/= {\\n  output: "export",/\' "$CONFIG_FILE" && npx next build && mv "${CONFIG_FILE}.bak" "$CONFIG_FILE"; else echo "Config file not found"; fi',
    description: 'Next.js',
    type: 'frontend'
  },
  vite: {
    command: 'vite build',
    description: 'Vite.js',
    type: 'frontend'
  },
  vue: {
    command: 'npm run build',
    description: 'Vue CLI',
    type: 'frontend'
  },
  fastapi: {
    command: 'uvicorn src.api.main:app --host 0.0.0.0 --port 8000',
    description: 'FastAPI',
    type: 'backend'
  },
  node: {
    command: 'npm start',
    description: 'Node.js',
    type: 'backend'
  },
  express: {
    command: 'node server.js',
    description: 'Express.js',
    type: 'backend'
  },
  django: {
    command: 'python manage.py runserver 0.0.0.0:8000',
    description: 'Django',
    type: 'backend'
  },
  flask: {
    command: 'flask run --host=0.0.0.0 --port=5000',
    description: 'Flask',
    type: 'backend'
  }
};

// Framework logo map (update to include backend logos)
const FRAMEWORK_LOGOS: Record<string, React.ReactNode> = {
  react: <ReactLogo />,
  angular: <AngularLogo />,
  nextjs: <NextLogo />,
  vite: <ViteLogo />,
  vue: <VueLogo />,
  fastapi: <FastAPILogo />,
  node: <NodeLogo />,
  express: <ExpressLogo />,
  django: <DjangoLogo />,
  flask: <FlaskLogo />
};

// Helper function to extract project name from root_path
const extractProjectNameFromPath = (rootPath: string): string => {
  if (!rootPath) return '';

  // Handle absolute paths by getting just the repo/project name
  if (rootPath.includes('/home/') || rootPath.includes('/Users/')) {
    // Try to extract the last meaningful directory name
    const segments = rootPath.split('/').filter(Boolean);
    if (segments.length > 0) {
      // Skip common path segments like 'home', 'workspace', etc.
      for (let i = segments.length - 1; i >= 0; i--) {
        const segment = segments[i];
        if (!['home', 'workspace', 'repos', 'users', 'src'].includes(segment.toLowerCase())) {
          return segment;
        }
      }
    }
  }

  // Split the path and get the last non-empty segment
  const segments = rootPath.split('/').filter(Boolean);
  if (segments.length > 0) {
    return segments[segments.length - 1];
  }
  return '';
};

const DeploymentInterface: React.FC<DeploymentInterfaceProps> = ({
  isOpen,
  onClose,
  selectedContainer,
}) => {

  const {
    // Repository data
    repositories,
    currentRepository,
    setCurrentRepository,
    deploymentPath,
    setDeploymentPath,
    isLoadingRepo,
    fetchRepositories,

    // Deployment status
    isDeploying,
    setIsDeploying,

    // Deployment configuration
    command,
    setCommand,
    installPackages,
    setInstallPackages,
    envVariables,
    setEnvVariables,

    // Directory browsing
    dirContents,
    currentPath,
    isLoadingDir,

    // Manifest data
    manifest,

    // WebSocket
    wsConnection,
    taskId,

    // Functions
    launchDeployment,
    fetchDirectoryContents,
    fetchManifest
  } = useDeployment();

  const { projectId } = useParams();
  const searchParams = useSearchParams();
  
  // Get container type from query parameters
  const containerType = (searchParams.get('type') === 'backend' ? 'backend' : 'frontend') as 'frontend' | 'backend'; // Default to frontend
  
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [isLoadingDeployments, setIsLoadingDeployments] = useState<boolean>(false);
  const [selectedDeployment, setSelectedDeployment] = useState<Deployment | null>(null);
  const [showDeploymentsDropdown, setShowDeploymentsDropdown] = useState<boolean>(false);
  const [hasTriedFetching, setHasTriedFetching] = useState<boolean>(false);
  const [fullCommand, setFullCommand] = useState('');
  const [showPlatformDropdown, setShowPlatformDropdown] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [savedConfigs, setSavedConfigs] = useState<PresetConfig[]>([]);
  const [showSavedConfigsDropdown, setShowSavedConfigsDropdown] = useState(false);
  const [showDirBrowser, setShowDirBrowser] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [showLaunchAnimation, setShowLaunchAnimation] = useState(false);
  const [deploymentError, setDeploymentError] = useState<string | null>(null);
  const [showRepositoryDropdown, setShowRepositoryDropdown] = useState(false);
  const [showFrameworkDropdown, setShowFrameworkDropdown] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);
  const [isCreateNewApp, setIsCreateNewApp] = useState(false);
  const [buildCommand, setBuildCommand] = useState('');

  // Get the current container configuration based on type
  const currentContainerConfig = containerType === 'backend' 
    ? manifest?.backend 
    : manifest?.frontend;

  // Update deployment type based on container type
  const getDeploymentType = () => {
    if (containerType === 'backend') {
      return currentContainerConfig?.framework === 'fastapi' ? 'fastapi_app' : 'backend_app';
    }
    return 'react_app'; // Default for frontend
  };

  // Filter frameworks based on container type
  const getFrameworksForType = () => {
    return Object.entries(FRAMEWORK_COMMANDS).filter(([_, config]) => 
      config.type === containerType
    );
  };

  // Update package installation logic based on container type
  const getPackageInstallCommand = () => {
    if (containerType === 'backend') {
      // For Python-based backends, use pip install
      if (currentContainerConfig?.framework === 'fastapi' || 
          currentContainerConfig?.framework === 'django' || 
          currentContainerConfig?.framework === 'flask') {
        return 'pip install -r requirements.txt';
      }
      // For Node.js-based backends
      return 'npm install';
    }
    // Default to npm install for frontend
    return 'npm install';
  };

  // Update full command generation based on container type
  useEffect(() => {
    let cmd = '';

    // Only add package installation for frontend containers
    if (containerType === 'frontend' && installPackages) {
      cmd = `${getPackageInstallCommand()} && `;
    }

    cmd += command;
    setFullCommand(cmd);
  }, [installPackages, command, containerType, currentContainerConfig]);

  // Set initial selectedFramework from manifest based on container type
  useEffect(() => {
    if (currentContainerConfig?.framework) {
      setSelectedFramework(currentContainerConfig.framework);
    }
  }, [currentContainerConfig?.framework]);

  // Update default command based on container type and framework
  useEffect(() => {
    if (containerType === 'backend') {
      // For backend, set both build and start commands
      if (currentContainerConfig?.buildCommand) {
        setBuildCommand(currentContainerConfig.buildCommand);
      } else {
        setBuildCommand('pip install -r requirements.txt');
      }
      if (currentContainerConfig?.startCommand) {
        let processedCommand = currentContainerConfig.startCommand;
        processedCommand = processedCommand.replace(/<host>/g, '0.0.0.0');
        if (currentContainerConfig.ports) {
          processedCommand = processedCommand.replace(/<port>/g, currentContainerConfig.ports.toString());
        }
        setCommand(processedCommand);
      } else {
        setCommand('uvicorn main:app --host 0.0.0.0 --port 8000');
      }
    } else {
      // Frontend logic
      if (currentContainerConfig?.framework === 'react') {
        setCommand('npm run build');
      } else if (currentContainerConfig?.framework === 'nextjs') {
        setCommand('npm run build');
      } else if (currentContainerConfig?.framework && FRAMEWORK_COMMANDS[currentContainerConfig.framework]) {
        const frameworkCommand = FRAMEWORK_COMMANDS[currentContainerConfig.framework].command;
        setCommand(frameworkCommand);
      } else {
        setCommand('npm run build');
      }
    }
  }, [currentContainerConfig?.startCommand, currentContainerConfig?.buildCommand, currentContainerConfig?.framework, currentContainerConfig?.ports, containerType, setCommand, setBuildCommand]);

  // Initialize empty environment variables - no defaults from manifest
  useEffect(() => {
    // Always start with empty environment variables
    // Users can add them manually if needed
    setEnvVariables([]);
  }, [containerType, setEnvVariables]);

  // Update installPackages default based on container type
  useEffect(() => {
    if (containerType === 'frontend') {
      // For frontend, keep install packages enabled
      setInstallPackages(true);
    }
    // For backend, don't set installPackages (it will remain false by default)
  }, [containerType, setInstallPackages]);

  // Update deployment path from manifest base_path based on container type
  useEffect(() => {
    if (manifest) {
      if (containerType === 'backend' && manifest.backend?.base_path) {
        setDeploymentPath(`/${manifest.backend.base_path}`);
      } else if (containerType === 'frontend' && manifest.frontend?.base_path) {
        setDeploymentPath(`/${manifest.frontend.base_path}`);
      }
    }
  }, [manifest, containerType, setDeploymentPath]);

  // Fetch deployments for the current project
  const fetchDeploymentsList = useCallback(async () => {
    // Skip if already tried or if still loading
    if (isLoadingDeployments) {
      return;
    }

    if (!currentRepository) {
      return;
    }

    if (!projectId) {
      return;
    }

    setIsLoadingDeployments(true);
    setHasTriedFetching(true);

    try {
      const response = await listDeployments(projectId);
      const sortedDeployments = response.sort((a: Deployment, b: Deployment) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setDeployments(sortedDeployments);

      // Auto-select the most recent deployment if deployments exist and none is currently selected
      if (sortedDeployments.length > 0 && !selectedDeployment) {
        setSelectedDeployment(sortedDeployments[0]);
      }
    } catch (error) {

    } finally {
      setIsLoadingDeployments(false);
    }
  }, [currentRepository, isLoadingDeployments, selectedDeployment]);

  // Only fetch deployments when the modal opens
  useEffect(() => {
    if (isOpen && !hasTriedFetching && currentRepository) {
      fetchDeploymentsList();
    }
  }, [isOpen, hasTriedFetching, currentRepository, fetchDeploymentsList]);

  // Auto-select the most recent deployment when deployments are available (only if user hasn't chosen to create new app)
  useEffect(() => {
    if (deployments.length > 0 && !selectedDeployment && !isCreateNewApp) {
      setSelectedDeployment(deployments[0]);
    }
  }, [deployments, selectedDeployment, isCreateNewApp]);

  // Reset the tried fetching flag when modal closes
  useEffect(() => {
    if (!isOpen) {
      setHasTriedFetching(false);
      // Reset selected folder when modal closes
      setSelectedFolder('');
      // Reset create new app state when modal closes
      setIsCreateNewApp(false);
    } else {
      // Fetch repositories when modal opens if we don't have any
      if (repositories.length === 0) {
        fetchRepositories();
      }

      // Fetch manifest data when modal opens
      fetchManifest();

      // Automatically fetch directory contents when modal opens
      if (currentRepository) {
        // Fetch directory contents as soon as modal opens
        fetchDirectoryContents(currentRepository.name);
      }
    }
  }, [isOpen, repositories.length, fetchRepositories, currentRepository, fetchDirectoryContents, fetchManifest]);

  // React when repository changes to fetch its contents
  useEffect(() => {
    if (isOpen && currentRepository) {
      fetchDirectoryContents(currentRepository.name);
    }
  }, [isOpen, currentRepository, fetchDirectoryContents]);

  // Set selected folder when modal opens with existing path
  useEffect(() => {
    if (showDirBrowser && deploymentPath) {
      // Extract the last part of the path as the selected folder
      const pathParts = deploymentPath.split('/').filter(Boolean);
      if (pathParts.length > 0) {
        setSelectedFolder(pathParts[pathParts.length - 1]);
      } else {
        setSelectedFolder('');
      }
    }
  }, [showDirBrowser, deploymentPath]);

  // Handle selection of first directory when directory contents are loaded
  useEffect(() => {
    if (taskId?.startsWith('cg') && dirContents.length > 0 && isOpen) {
      // First try to find a folder that's not named "logs" and not hidden
      let folderToSelect = dirContents.find((item: DirContent) =>
        item.type === 'folder' && !item.name.startsWith('.') && item.name.toLowerCase() !== 'logs'
      );

      // If no folder other than "logs" is found, fall back to the first folder
      if (!folderToSelect) {
        folderToSelect = dirContents.find((item: DirContent) =>
          item.type === 'folder' && !item.name.startsWith('.')
        );
      }

      if (folderToSelect) {
        // Set the selected folder as deployment path
        const newPath = `/${folderToSelect.name}`;
        setDeploymentPath(newPath);
        setSelectedFolder(folderToSelect.name);
      }
    }
  }, [dirContents, taskId, isOpen, setDeploymentPath]);

  // Update selectedFolder when deploymentPath changes
  useEffect(() => {
    if (deploymentPath && deploymentPath !== '/') {
      // Extract the folder name from the path
      const pathParts = deploymentPath.split('/').filter(Boolean);
      if (pathParts.length > 0) {
        setSelectedFolder(pathParts[pathParts.length - 1]);
      }
    }
  }, [deploymentPath]);

  // Apply settings from selected deployment
  const selectDeployment = (deployment: Deployment) => {
    setSelectedDeployment(deployment);

    // Set command from deployment
    if (deployment.command) {
      // Check if command includes 'npm install &&' to determine installPackages
      const hasInstallCommand = deployment.command.includes('npm install &&');
      setInstallPackages(hasInstallCommand);

      // Extract the actual build command
      let deploymentCommand = deployment.command;
      if (hasInstallCommand) {
        deploymentCommand = deployment.command.split('npm install &&').pop()?.trim() || '';
      }

      // Set the command without the install part
      setCommand(deploymentCommand);
    }

    // Set deployment path
    if (deployment.root_path) {
      // Extract relative path from the root_path
      let relativePath = deployment.root_path;

      if (currentRepository?.name && relativePath.includes(currentRepository.name)) {
        const repoIndex = relativePath.indexOf(currentRepository.name);
        const pathAfterRepo = relativePath.substring(repoIndex + currentRepository.name.length);
        relativePath = pathAfterRepo || '/';
      }

      setDeploymentPath(relativePath);
    }

    // Close the dropdown
    setShowDeploymentsDropdown(false);
  };

  // Listen for deployment status messages from WebSocket
  useEffect(() => {
    if (!wsConnection) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);

        // Handle deployment status updates (original format)
        if (message.type === 'deployment_status') {

          // Extract status from the message based on the data structure
          const statusData = message.data || message;
          const status = statusData.status || message.status;

          // Handle different status updates
          if (status === 'success' || status === 'completed') {
            // Handle successful deployment
            setIsDeploying(false);
            setShowLaunchAnimation(false);
            onClose();
          } else if (status === 'failed' || status === 'error') {
            // Handle failed deployment

            setIsDeploying(false);
            setShowLaunchAnimation(false);
            setDeploymentError(`Deployment failed: ${statusData.message || 'Unknown error'}`);
          } else if (status === 'processing' || status === 'in_progress' || status === 'deploying') {
            // Handle in-progress deployment - close the modal when status is deploying or processing
            setIsDeploying(true);

            // Close the modal when deployment starts processing
            setShowLaunchAnimation(false);
            onClose();
          }
        }

        // Handle deploy_status updates (from Python code example)
        else if (message.type === 'deploy_status') {

          const statusData = message.data || message;
          const status = statusData.status || message.status;

          if (status === 'success' || status === 'completed') {
            // Handle successful deployment
            setIsDeploying(false);
            setShowLaunchAnimation(false);
            onClose();
          } else if (status === 'failed' || status === 'error') {
            // Handle failed deployment

            setIsDeploying(false);
            setShowLaunchAnimation(false);
            setDeploymentError(`Deployment failed: ${statusData.message || 'Unknown error'}`);
          } else if (status === 'processing' || status === 'in_progress' || status === 'building' || status === 'deploying') {
            // Handle in-progress deployment - close the modal when deployment is in progress
            setIsDeploying(true);

            // Close the modal when deployment starts processing
            setShowLaunchAnimation(false);
            onClose();
          }
        }
        
        // Handle project configuration responses (main logic is in DeploymentContext)
        else if (message.type === 'manifest') {
          // Project configuration is handled in the DeploymentContext, 
          // but we can add any UI-specific handling here if needed
          console.log('Project configuration received:', message.data);
        }

        // Handle start_deploy_response
        else if (message.type === 'start_deploy_response') {

          if (message.status === 'success' || message.success === true) {
            // Deployment initiated successfully
            setIsDeploying(true);

            // Close the modal immediately upon successful start_deploy response
            setShowLaunchAnimation(false);
            onClose();
          } else {
            // Failed to initiate deployment

            setIsDeploying(false);
            setShowLaunchAnimation(false);
            setDeploymentError(`Failed to start deployment: ${message.message || 'Unknown error'}`);
          }
        }
      } catch (error) {

      }
    };

    wsConnection.addEventListener('message', handleMessage);

    return () => {
      wsConnection.removeEventListener('message', handleMessage);
    };
  }, [wsConnection, setIsDeploying, onClose, isDeploying]);

  // Load saved configurations
  useEffect(() => {
    const storedConfigs = localStorage.getItem('deploymentConfigs');
    if (storedConfigs) {
      try {
        setSavedConfigs(JSON.parse(storedConfigs));
      } catch (e) {

      }
    }
  }, []);

  const addEnvVariable = () => {
    setEnvVariables([
      ...envVariables,
      { key: '', value: '', id: Date.now().toString() }
    ]);
  };

  const removeEnvVariable = (id: string) => {
    setEnvVariables(envVariables.filter((v: EnvVariable) => v.id !== id));
  };

  const updateEnvVariable = (id: string, field: 'key' | 'value', newValue: string) => {
    setEnvVariables(
      envVariables.map((v: EnvVariable) =>
        v.id === id ? { ...v, [field]: newValue } : v
      )
    );
  };

  // Create environment variables string for preview
  const getEnvString = () => {
    return envVariables
      .filter((v: EnvVariable) => v.key.trim() !== '')
      .map((v: EnvVariable) => `${v.key}=${v.value}`)
      .join(' ');
  };

  // Apply platform preset
  const applyPlatformPreset = (platformKey: string) => {
    const preset = PLATFORM_PRESETS[platformKey];
    if (!preset) return;

    setSelectedPlatform(platformKey);
    setCommand(preset.command);
    setInstallPackages(preset.installPackages);

    // Deep clone the env variables to avoid reference issues
    const newEnvVars = preset.envVariables.map((v: EnvVariable) => ({
      ...v,
      // Replace ${repository_name} with actual repository name if available
      value: v.value.replace('${repository_name}', currentRepository?.name || '')
    }));

    setEnvVariables(newEnvVars);
    setDeploymentPath(preset.deploymentPath);
    setShowPlatformDropdown(false);
  };

  // Save current configuration
  const saveCurrentConfig = () => {
    // Prompt for configuration name
    const configName = prompt('Enter a name for this configuration:');
    if (!configName) return;

    const newConfig: PresetConfig = {
      name: configName,
      command,
      installPackages,
      envVariables: JSON.parse(JSON.stringify(envVariables)),
      deploymentPath
    };

    const updatedConfigs = [...savedConfigs, newConfig];
    setSavedConfigs(updatedConfigs);

    // Save to localStorage
    try {
      localStorage.setItem('deploymentConfigs', JSON.stringify(updatedConfigs));
    } catch (e) {

    }
  };

  // Apply saved configuration
  const applySavedConfig = (config: PresetConfig) => {
    setCommand(config.command);
    setInstallPackages(config.installPackages);
    setEnvVariables(JSON.parse(JSON.stringify(config.envVariables)));
    setDeploymentPath(config.deploymentPath);
    setShowSavedConfigsDropdown(false);
  };

  const navigateToPath = (folderName: string) => {
    let newPath = currentPath;

    // Handle navigation logic
    if (folderName === '..') {
      // Go up one directory
      const pathParts = currentPath.split('/');
      pathParts.pop();
      newPath = pathParts.join('/');
      // Clear selection when going up
      setSelectedFolder('');
    } else {
      // Go into the folder
      newPath = `${currentPath}/${folderName}`.replace(/\/+/g, '/');
    }

    fetchDirectoryContents(newPath);
  };

  const handleFolderSelect = (folderName: string) => {
    // Toggle selection
    if (selectedFolder === folderName) {
      setSelectedFolder('');
    } else {
      setSelectedFolder(folderName);
    }
  };

  const selectPath = (path: string) => {
    let relativePath = path;
    let selectedFolderPath = '';

    if (selectedFolder) {
      // If a specific folder is selected, append it to the path
      selectedFolderPath = `${path}/${selectedFolder}`.replace(/\/+/g, '/');
    } else {
      // Otherwise use the current path
      selectedFolderPath = path;
    }

    // Convert the repository path to relative path
    if (currentRepository?.name && selectedFolderPath.includes(currentRepository.name)) {
      const repoIndex = selectedFolderPath.indexOf(currentRepository.name);
      relativePath = selectedFolderPath.substring(repoIndex);

      // If it starts with the repo name, make it relative
      if (relativePath.startsWith(currentRepository.name)) {
        relativePath = relativePath.replace(currentRepository.name, '');
      }
    } else {
      relativePath = selectedFolderPath;
    }

    // Ensure the path starts with a slash
    if (!relativePath.startsWith('/')) {
      relativePath = '/' + relativePath;
    }

    // If no folder is explicitly selected and we're at root, use "/"
    if (!selectedFolder && (relativePath === '' || relativePath === '/' || relativePath === '/')) {
      relativePath = '/';
    }

    setDeploymentPath(relativePath || '/');
    setShowDirBrowser(false);
    setSelectedFolder('');
  };

  // Check if the current path is root
  const isRootPath = () => {
    // Check if we're at the repo root or absolute root
    return !currentPath ||
           currentPath === '/' ||
           currentPath === currentRepository?.name ||
           currentPath?.endsWith(`/${currentRepository?.name}`);
  };

  // Format the display of deployment path to only show relative path
  const formatDisplayPath = (path: string): string => {
    if (!path) return '/';

    // For absolute paths that look like file system paths (e.g., /home/<USER>
    if (path.includes('/home/') || path.includes('/Users/')) {
      return '/';
    }

    // For paths that contain the repository name, strip it out
    if (currentRepository?.name && path.includes(currentRepository.name)) {
      // Find where the repo name occurs and take everything after it
      const pathParts = path.split(currentRepository.name);
      if (pathParts.length > 1) {
        const afterRepo = pathParts[pathParts.length - 1];
        return afterRepo || '/';
      }
    }

    // If it's already a relative path starting with slash, just return it
    if (path.startsWith('/')) return path;

    // Otherwise, add a leading slash
    return `/${path}`;
  };

  // Helper function to generate UUID
  const generateUUID = () => {
    // Simple UUID generation that's compatible with all browsers
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  };

  // Handle launch deployment button
  const handleLaunch = () => {

    // Reset any previous errors
    setDeploymentError(null);

    // Generate unique ID for the deployment, but only if we're creating a new app
    // If updating an existing app, we'll use its ID
    const deployment_id = selectedDeployment ? selectedDeployment.deployment_id : generateUUID();

    // Send the deployment data via WebSocket
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      try {
        if (containerType === 'backend') {
          // Backend deployment - use start_container_deployment
          const backendPayload = {
            type: "start_container_deployment",
            task_id: taskId,
            input_data: {
              build_command: buildCommand,
              run_command: command, // Use the start command for run_command
              port: currentContainerConfig?.ports?.toString() || "3001",
              framework: currentContainerConfig?.framework || "fastapi",
              base_path: currentContainerConfig?.base_path || "",
            }
          };

          wsConnection.send(JSON.stringify(backendPayload));
        } else {
          // Frontend deployment - use existing start_deploy
          // Create environment variables string for command
          const envString = envVariables
            .filter((v: EnvVariable) => v.key.trim() !== '')
            .map((v: EnvVariable) => `${v.key}=${v.value}`)
            .join(' ');

          // Create full command with env variables
          let commandString = '';
          if (containerType === 'frontend' && installPackages) {
            const installCmd = getPackageInstallCommand();
            commandString = `${installCmd} && ${envString ? `${envString} ${command}` : command}`;
          } else {
            commandString = envString ? `${envString} ${command}` : command;
          }

          // Create the deployment payload with dynamic deployment type
          const deploymentPayload: {
            id: string;
            deployment_type: string;
            command: string;
            root_path: string;
            env_variables: Record<string, string>;
            app_id?: string;
          } = {
            id: deployment_id,
            deployment_type: getDeploymentType(),
            command: commandString,
            root_path: currentRepository?.path
              ? `${currentRepository.path}${deploymentPath.startsWith('/') ? deploymentPath : `/${deploymentPath}`}`.replace(/\/+/g, '/')
              : deploymentPath,
            env_variables: envVariables.reduce((acc: Record<string, string>, v: EnvVariable) => {
              if (v.key.trim()) {
                acc[v.key] = v.value;
              }
              return acc;
            }, {})
          };

          // If updating an existing deployment, include the app_id
          if (selectedDeployment && selectedDeployment.app_id) {
            deploymentPayload.app_id = selectedDeployment.app_id;
          }

          const message = {
            type: "start_deploy",
            task_id: taskId,
            input_data: deploymentPayload
          };

          wsConnection.send(JSON.stringify(message));
        }

        // Set the deploying state
        setIsDeploying(true);
        setShowLaunchAnimation(true);
        
        // For backend deployments, stop loader after animation since WebSocket responses aren't reliable
        if (containerType === 'backend') {
          setTimeout(() => {
            setShowLaunchAnimation(false);
            setIsDeploying(false);
            onClose();
          }, 3000);
        } else {
          // For frontend, hide animation but wait for WebSocket response
          setTimeout(() => {
            setShowLaunchAnimation(false);
          }, 3000);
        }

      } catch (error) {

        setDeploymentError('Failed to send deployment data. Please try again.');
        setShowLaunchAnimation(false);
      }
    } else {

      setDeploymentError('WebSocket connection is not available. Please refresh the page and try again.');
      setShowLaunchAnimation(false);
    }
  };

  // Rocket Launch Animation Component
  const RocketLaunchAnimation = () => {
    return (
      <div className="absolute inset-0 flex items-center justify-center z-20 backdrop-blur-md bg-white bg-opacity-75">
        <div className="relative flex flex-col items-center">
          {/* Enhanced rocket and flame */}
          <div className="relative mb-10 h-48">
            {/* Multiple flame layers for better effect */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-28 rounded-b-full bg-gradient-to-t from-orange-600 via-orange-500 to-transparent opacity-60 animate-pulse"></div>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-20 rounded-b-full bg-gradient-to-t from-yellow-500 via-yellow-400 to-transparent opacity-80 animate-flame"></div>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-5 h-16 rounded-b-full bg-gradient-to-t from-red-500 via-orange-300 to-transparent opacity-70 animate-flame-delay"></div>

            {/* Simple, clear rocket design */}
            <div className="relative z-10 animate-rocket">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="60" height="60" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
                {/* Simple rocket based on Lucide Rocket icon */}
                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" stroke="#F26A1B" fill="#F97316" />
                <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" stroke="#F26A1B" fill="#F97316" />
                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" stroke="#F26A1B" fill="#F97316" />
                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" stroke="#F26A1B" fill="#F97316" />
              </svg>
            </div>

            {/* Small particles/sparks for enhanced effect */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-20 h-8 flex justify-center">
              <div className="w-1 h-1 bg-yellow-300 rounded-full animate-spark-1 opacity-80"></div>
              <div className="w-1 h-1 bg-orange-400 rounded-full animate-spark-2 opacity-80"></div>
              <div className="w-1 h-1 bg-red-400 rounded-full animate-spark-3 opacity-80"></div>
              <div className="w-1 h-1 bg-yellow-300 rounded-full animate-spark-4 opacity-80"></div>
            </div>
          </div>

          <div className="text-orange-500 font-weight-medium typography-body-lg">
            Launching Deployment
          </div>
          <div className="text-gray-600 mt-2 typography-body-sm">
            Preparing to blast off!
          </div>
        </div>
      </div>
    );
  };

  // Handle repository selection
  const handleRepositorySelect = (repo: any) => {
    setCurrentRepository(repo);
    setShowRepositoryDropdown(false);
    // Reset deployment path when switching repositories
    setDeploymentPath('/');
    // Reset any errors
    setDeploymentError(null);
    // Reset selected deployment when changing repositories
    setSelectedDeployment(null);
    // Reset create new app state when changing repositories
    setIsCreateNewApp(false);
    // Reset fetch flag to allow a new fetch
    setHasTriedFetching(false);

    // Fetch manifest data for the new repository
    fetchManifest();

    // The fetchDeploymentsList will be triggered by useEffect when currentRepository changes
  };

  return (
    <>
      <style jsx global>{`
        @keyframes rocket {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-15px);
          }
          100% {
            transform: translateY(0px);
          }
        }

        .animate-rocket {
          animation: rocket 2s infinite ease-in-out;
        }

        @keyframes flame {
          0% {
            height: 16px;
            opacity: 0.7;
          }
          50% {
            height: 22px;
            opacity: 0.9;
          }
          100% {
            height: 16px;
            opacity: 0.7;
          }
        }

        .animate-flame {
          animation: flame 0.5s infinite ease-in-out;
        }

        @keyframes flame-delay {
          0% {
            height: 12px;
            opacity: 0.6;
          }
          50% {
            height: 18px;
            opacity: 0.8;
          }
          100% {
            height: 12px;
            opacity: 0.6;
          }
        }

        .animate-flame-delay {
          animation: flame-delay 0.5s infinite ease-in-out 0.15s;
        }

        @keyframes spark-1 {
          0% { transform: translate(-5px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(-10px, 10px); opacity: 0; }
        }

        @keyframes spark-2 {
          0% { transform: translate(5px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(12px, 12px); opacity: 0; }
        }

        @keyframes spark-3 {
          0% { transform: translate(-2px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(-7px, 15px); opacity: 0; }
        }

        @keyframes spark-4 {
          0% { transform: translate(8px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(15px, 10px); opacity: 0; }
        }

        .animate-spark-1 {
          animation: spark-1 1s infinite ease-out;
        }

        .animate-spark-2 {
          animation: spark-2 1.3s infinite ease-out 0.2s;
        }

        .animate-spark-3 {
          animation: spark-3 0.9s infinite ease-out 0.5s;
        }

        .animate-spark-4 {
          animation: spark-4 1.1s infinite ease-out 0.1s;
        }
      `}</style>

      <Drawer
        bodyClass='scrollbar-hide overflow-y-auto'
        isOpen={isOpen}
        onClose={() => {
          onClose();
        }}
        placement="right"
        overlayClassName="z-[1001] left-0"
        width={450}
        showBackdrop={false}
        theme="light"
        title={
          <div className="flex items-center gap-2">
            <span className="font-weight-medium">
              {containerType === 'backend' ? 'Backend' : 'Frontend'} Deployment Configuration
            </span>
          </div>
        }
      >
        <div className="flex flex-col h-full relative">
          {/* Rocket Launch Animation Overlay */}
          {showLaunchAnimation && <RocketLaunchAnimation />}

          <div className="overflow-y-auto pb-24 px-[20px] py-[16px]">
            {/* Header Info */}
            <div className="bg-orange-50 p-4 rounded-md mb-6">
              <div className="flex items-center gap-2 mb-2">
                <Rocket className="h-5 w-5 text-orange-500" />
                <h3 className="font-weight-semibold text-[14px] text-gray-800">
                  Configure {containerType === 'backend' ? 'Backend' : 'Frontend'} Deployment
                </h3>
              </div>
              <p className="typography-body-sm text-gray-600">
                Configure your {containerType} deployment settings for {currentRepository?.name || 'your project'}
              </p>
              {currentContainerConfig && (
                <div className="mt-2 flex items-center gap-2">
                  <span className="typography-caption text-gray-500">Framework:</span>
                  <div className="flex items-center gap-1">
                    {FRAMEWORK_LOGOS[currentContainerConfig.framework]}
                    <span className="typography-caption font-weight-medium text-gray-700 capitalize">
                      {currentContainerConfig.framework}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Selected Container Info */}
            {selectedContainer && (
              <div className="bg-blue-50 p-4 rounded-md mb-6 border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <h3 className="font-weight-semibold text-[14px] text-gray-800">Selected Container</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="typography-body-sm font-weight-medium text-gray-700">
                      {selectedContainer.name || 'Container'}
                    </span>
                    <span className="px-2 py-1 typography-caption font-weight-medium bg-green-100 text-green-800 rounded-full">
                      Active
                    </span>
                  </div>
                  {selectedContainer.port && (
                    <p className="typography-caption text-gray-600">
                      Port: {selectedContainer.port}
                    </p>
                  )}
                  {selectedContainer.status && (
                    <p className="typography-caption text-gray-600">
                      Status: {selectedContainer.status}
                    </p>
                  )}
                  {selectedContainer.url && (
                    <div className="flex items-center gap-2">
                      <span className="typography-caption text-gray-600">URL:</span>
                      <a
                        href={processPreviewUrl(selectedContainer.url)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="typography-caption text-blue-600 hover:text-blue-800 underline truncate"
                      >
                        {processPreviewUrl(selectedContainer.url)}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Existing Deployments Section */}
            {deployments.length > 0 && (
              <div className="mb-6">
                <div className="border-b pb-2 mb-3">
                  <h2 className="font-weight-semibold text-[14px] text-gray-800">Existing Deployments</h2>
                </div>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex flex-col">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="createNewApp"
                        checked={isCreateNewApp}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setIsCreateNewApp(checked);

                          if (checked) {
                            // Create new app - clear selected deployment
                            setSelectedDeployment(null);
                          } else {
                            // Update existing app - select the most recent deployment
                            if (deployments.length > 0) {
                              setSelectedDeployment(deployments[0]);
                            }
                          }
                        }}
                        className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
                      />
                      <label htmlFor="createNewApp" className="ml-2 typography-body-sm text-gray-700 cursor-pointer">
                        Create New App
                      </label>
                    </div>
                    <div className="ml-6 typography-caption text-gray-500 mt-1">
                      {isCreateNewApp
                        ? 'Creating a new app deployment'
                        : 'Updating the most recent deployment. Check above to create new instead.'}
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <div className="flex gap-2">
                    <button
                      onClick={() => setShowDeploymentsDropdown(!showDeploymentsDropdown)}
                      disabled={isCreateNewApp}
                      className={`flex-1 p-3 rounded-md flex items-center justify-between typography-body-sm text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1 ${isCreateNewApp ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'}`}
                    >
                      <div className="flex items-center gap-2">
                        {isLoadingDeployments ? (
                          <>
                            <div className="h-4 w-4 rounded-full border-2 border-orange-500 border-t-transparent animate-spin"></div>
                            <span>Loading deployments...</span>
                          </>
                        ) : (
                          <>
                            <History size={16} className={`${isCreateNewApp ? 'text-gray-400' : 'text-orange-500'}`} />
                            <span className="max-w-[250px]">
                              {selectedDeployment ? (
                                <div className="flex items-center gap-1">
                                  <div className="font-weight-medium max-w-[200px] truncate">
                                    <span className="font-weight-semibold truncate">{extractProjectNameFromPath(selectedDeployment.root_path)}</span>
                                    {selectedDeployment.app_id && (
                                      <span className="font-weight-normal text-gray-600 ml-1 truncate">- {selectedDeployment.app_id}</span>
                                    )}
                                  </div>
                                  <span className="typography-caption text-gray-500 ml-1 whitespace-nowrap">
                                    ({new Date(selectedDeployment.created_at).toLocaleDateString()})
                                  </span>
                                </div>
                              ) : (
                                'Select existing deployment'
                              )}
                            </span>
                          </>
                        )}
                      </div>
                      <ChevronDown size={16} className={`${isCreateNewApp ? 'text-gray-400' : 'text-orange-500'} transition-transform ${showDeploymentsDropdown ? 'rotate-180' : ''}`} />
                    </button>
                    <button
                      onClick={() => {
                        setHasTriedFetching(false);
                        fetchDeploymentsList();
                        if (selectedDeployment) {
                          setShowDeploymentsDropdown(true);
                        }
                      }}
                      disabled={isCreateNewApp}
                      title="Refresh deployments"
                      className={`p-3 rounded-md text-gray-600 transition-colors ${isCreateNewApp ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-50 hover:bg-gray-100'}`}
                    >
                      <RefreshCw size={16} className={isLoadingDeployments ? "animate-spin" : ""} />
                    </button>
                  </div>

                  {showDeploymentsDropdown && !isCreateNewApp && (
                    <div className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                      {deployments.length > 0 ? (
                        <ul className="py-1">
                          {deployments.map((deployment: Deployment) => (
                            <li key={deployment.deployment_id} className="border-b border-gray-100 last:border-b-0">
                              <button
                                onClick={() => selectDeployment(deployment)}
                                className={`w-full text-left px-4 py-3 typography-body-sm hover:bg-gray-50 flex items-center gap-3 ${
                                  selectedDeployment?.deployment_id === deployment.deployment_id ? 'bg-orange-50 text-orange-600' : ''
                                }`}
                              >
                                <div className="flex-shrink-0">
                                  {selectedDeployment?.deployment_id === deployment.deployment_id ? (
                                    <Check size={18} className="text-orange-500" />
                                  ) : (
                                    <Rocket size={18} className={
                                      deployment.status === 'success' ? 'text-green-500' :
                                      deployment.status === 'failed' ? 'text-red-500' :
                                      deployment.status === 'processing' ? 'text-blue-500' : 'text-gray-500'
                                    } />
                                  )}
                                </div>
                                <div className="flex flex-col flex-1 min-w-0">
                                  <div className="font-weight-medium typography-body-sm truncate">
                                    {/* Display project name and app_id clearly */}
                                    <span className="font-weight-semibold truncate">{extractProjectNameFromPath(deployment.root_path)}</span>
                                    {deployment.app_id && (
                                      <span className="font-weight-normal text-gray-600 ml-1 truncate">- {deployment.app_id}</span>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2 typography-caption text-gray-500 mt-1">
                                    <span className="whitespace-nowrap">{new Date(deployment.created_at).toLocaleDateString()}</span>
                                    <span className="w-1 h-1 rounded-full bg-gray-300 flex-shrink-0"></span>
                                    <span className={`font-weight-medium whitespace-nowrap ${
                                      deployment.status === 'success' ? 'text-green-500' :
                                      deployment.status === 'failed' ? 'text-red-500' :
                                      deployment.status === 'processing' ? 'text-blue-500' : 'text-gray-500'
                                    }`}>
                                      {deployment.status}
                                    </span>
                                  </div>
                                </div>
                              </button>
                            </li>
                          ))}
                        </ul>
                      ) : isLoadingDeployments ? (
                        <div className="p-4 text-center text-gray-500">
                          <div className="inline-block h-4 w-4 rounded-full border-2 border-orange-500 border-t-transparent animate-spin mr-2"></div>
                          Loading deployments...
                        </div>
                      ) : (
                        <div className="p-4 text-center">
                          <p className="text-gray-500 mb-2">No deployments available</p>
                          <button
                            onClick={() => {
                              setHasTriedFetching(false);
                              fetchDeploymentsList();
                            }}
                            className="px-3 py-1 typography-caption text-orange-600 border border-orange-300 rounded-md hover:bg-orange-50"
                          >
                            <RefreshCw size={12} className="inline mr-1" />
                            Refresh
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                <p className="typography-caption text-gray-500 mt-2">
                  {!isCreateNewApp ? 'Select a different deployment to update, or check "Create New App" above to create a new one' : 'Check "Create New App" is selected above to create a new deployment'}
                </p>
              </div>
            )}

            {/* Repository Section with Dropdown */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">Repository</h2>
              </div>
              <div className="relative">
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setShowRepositoryDropdown(!showRepositoryDropdown);
                      if (repositories.length === 0) {
                        fetchRepositories();
                      }
                    }}
                    className="flex-1 bg-gray-50 p-3 rounded-md flex items-center justify-between typography-body-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      {isLoadingRepo ? (
                        <>
                          <div className="h-4 w-4 rounded-full border-2 border-orange-500 border-t-transparent animate-spin"></div>
                          <span>Loading repositories...</span>
                        </>
                      ) : (
                        <>
                          <Folder size={16} className="text-gray-500" />
                          <span>{currentRepository?.name || 'Select a repository'}</span>
                        </>
                      )}
                    </div>
                    <ChevronDown size={16} className={`text-gray-500 transition-transform ${showRepositoryDropdown ? 'rotate-180' : ''}`} />
                  </button>
                  <button
                    onClick={() => {
                      fetchRepositories();
                      setShowRepositoryDropdown(true);
                    }}
                    title="Refresh repositories"
                    className="p-3 bg-gray-50 rounded-md text-gray-600 hover:bg-gray-100 transition-colors"
                  >
                    <RefreshCw size={16} className={isLoadingRepo ? "animate-spin" : ""} />
                  </button>
                </div>

                {showRepositoryDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                    {repositories.length > 0 ? (
                      <ul className="py-1">
                        {repositories.map((repo: any, index: number) => (
                          <li key={index}>
                            <button
                              onClick={() => handleRepositorySelect(repo)}
                              className={`w-full text-left px-3 py-2 typography-body-sm hover:bg-gray-50 flex items-center gap-2 ${
                                currentRepository?.name === repo.name ? 'bg-orange-50 text-orange-600' : ''
                              }`}
                            >
                              <Folder size={16} className={currentRepository?.name === repo.name ? 'text-orange-500' : 'text-gray-500'} />
                              <span>{repo.name}</span>
                            </button>
                          </li>
                        ))}
                      </ul>
                    ) : isLoadingRepo ? (
                      <div className="p-3 text-center text-gray-500">
                        <div className="inline-block h-4 w-4 rounded-full border-2 border-orange-500 border-t-transparent animate-spin mr-2"></div>
                        Loading repositories...
                      </div>
                    ) : (
                      <div className="p-3 text-center">
                        <p className="text-gray-500 mb-2">No repositories available</p>
                        <button
                          onClick={fetchRepositories}
                          className="px-3 py-1 typography-caption text-orange-600 border border-orange-300 rounded-md hover:bg-orange-50"
                        >
                          <RefreshCw size={12} className="inline mr-1" />
                          Refresh
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <p className="typography-caption text-gray-500 mt-2">
                Select the repository you want to deploy
              </p>
            </div>

            {/* Deployment Path */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex items-center justify-between">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">
                  Deployment Path ({containerType === 'backend' ? 'Backend' : 'Frontend'})
                </h2>
                <div className="flex items-center gap-2">
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={formatDisplayPath(deploymentPath)}
                    onChange={(e) => {
                      // When user edits, treat it as a relative path
                      const newPath = e.target.value;
                      setDeploymentPath(newPath);
                    }}
                    placeholder="/"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm"
                  />
                  <button
                    onClick={() => {
                      setShowDirBrowser(true);
                      setTimeout(() => {
                        fetchDirectoryContents(currentRepository?.name);
                      }, 100);
                    }}
                    className="px-3 py-2 typography-body-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-orange-500 relative"
                    title="Browse folders"
                    disabled={isLoadingDir}
                  >
                    {isLoadingDir ? (
                      <div className="animate-spin h-4 w-4 border-2 border-orange-500 border-t-transparent rounded-full mx-auto"></div>
                    ) : (
                      <Folder size={16} className="text-gray-600" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Directory Browser Modal */}
            {showDirBrowser && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-4 m-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-weight-medium text-gray-900">Select Deployment Folder</h3>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${
                        wsConnection?.readyState === WebSocket.OPEN
                          ? 'bg-green-500'
                          : 'bg-red-500'
                      }`}></div>
                      <button
                        onClick={() => fetchDirectoryContents(currentPath || currentRepository?.name)}
                        className="text-gray-500 hover:text-gray-700 p-1 relative"
                        title="Refresh"
                        disabled={isLoadingDir}
                      >
                        {isLoadingDir ? (
                          <div className="animate-spin h-4 w-4 border-2 border-orange-500 border-t-transparent rounded-full"></div>
                        ) : (
                          <RefreshCw size={14} />
                        )}
                      </button>
                      <button
                        onClick={() => setShowDirBrowser(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  </div>

                  <div className="typography-caption text-gray-500 mb-2 flex justify-between">
                    <span>Select a folder or double-click to navigate</span>
                    {selectedFolder && <span className="text-orange-600">Selected: {selectedFolder}</span>}
                  </div>

                  <div className="border border-gray-200 rounded-md max-h-60 overflow-y-auto mb-4">
                    {isLoadingDir ? (
                      <div className="p-4 text-center text-gray-500">
                        <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500 mr-2"></div>
                        Loading...
                      </div>
                    ) : dirContents.length > 0 ? (
                      <ul className="divide-y divide-gray-200">
                        {/* Parent directory option - only show if not at root */}
                        {currentPath && !isRootPath() && (
                          <li
                            className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-2"
                            onClick={() => navigateToPath('..')}
                          >
                            <Folder size={16} className="text-gray-500" />
                            <span className="text-gray-500">..</span>
                          </li>
                        )}

                        {/* Filter to only show folders and exclude hidden folders (starting with .) */}
                        {dirContents
                          .filter((item: DirContent) => item.type === 'folder' && !item.name.startsWith('.'))
                          .map((item: DirContent, index: number) => (
                            <li
                              key={index}
                              className={`px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-2 ${
                                selectedFolder === item.name ? 'bg-orange-50' : ''
                              }`}
                              onClick={() => handleFolderSelect(item.name)}
                              onDoubleClick={() => navigateToPath(item.name)}
                            >
                              <div className="flex items-center justify-center w-5 h-5">
                                <input
                                  type="checkbox"
                                  checked={selectedFolder === item.name}
                                  onChange={() => handleFolderSelect(item.name)}
                                  className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </div>
                              <Folder size={16} className={`${selectedFolder === item.name ? 'text-orange-500' : 'text-gray-500'}`} />
                              <span className={selectedFolder === item.name ? 'font-weight-medium text-orange-700' : ''}>{item.name}</span>
                            </li>
                          ))
                        }
                      </ul>
                    ) : (
                      <div className="p-4 text-center">
                        <p className="text-gray-500 mb-2">No folders available</p>
                        <button
                          onClick={() => fetchDirectoryContents(currentRepository?.name)}
                          className="px-3 py-1 typography-caption text-orange-600 border border-orange-300 rounded-md hover:bg-orange-50 flex items-center justify-center gap-1"
                          disabled={isLoadingDir}
                        >
                          {isLoadingDir ? (
                            <div className="animate-spin h-3 w-3 border-2 border-orange-500 border-t-transparent rounded-full mr-1"></div>
                          ) : (
                            <RefreshCw size={12} />
                          )}
                          Retry
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-3">
                    <button
                      onClick={() => setShowDirBrowser(false)}
                      className="px-3 py-1.5 typography-body-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => selectPath(currentPath)}
                      className="px-3 py-1.5 typography-body-sm text-white bg-orange-500 hover:bg-orange-600 rounded-md"
                    >
                      {selectedFolder
                        ? `Select "${selectedFolder}"`
                        : isRootPath()
                          ? 'Select Root (/)'
                          : 'Select Current Path'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Environment Variables */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex justify-between items-center">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">Environment Variables</h2>
                <button
                  onClick={addEnvVariable}
                  className="text-orange-500 hover:text-orange-600 transition-colors focus:outline-none"
                >
                  <Plus size={16} />
                </button>
              </div>
              <div className="space-y-3">
                {envVariables.map((variable: EnvVariable) => (
                  <div key={variable.id} className="flex items-center gap-2">
                    <input
                      type="text"
                      value={variable.key}
                      onChange={(e) => updateEnvVariable(variable.id, 'key', e.target.value)}
                      placeholder="KEY"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm uppercase"
                    />
                    <span className="text-gray-400">=</span>
                    <input
                      type="text"
                      value={variable.value}
                      onChange={(e) => updateEnvVariable(variable.id, 'value', e.target.value)}
                      placeholder="value"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm"
                    />
                    <button
                      onClick={() => removeEnvVariable(variable.id)}
                      className="text-gray-400 hover:text-red-500 transition-colors focus:outline-none"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
                {envVariables.length === 0 && (
                  <p className="typography-body-sm text-gray-500 italic">No environment variables defined</p>
                )}
                <p className="typography-caption text-gray-500">
                  Define environment variables for your deployment
                </p>
              </div>
            </div>

            {/* Command */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex items-center justify-between">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">
                  Start Command
                </h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFrameworkDropdown(!showFrameworkDropdown)}
                    className="typography-caption bg-gray-50 border border-gray-200 rounded-md px-2 py-1 text-gray-600 hover:text-orange-500 hover:border-orange-300 flex items-center gap-1"
                  >
                    {selectedFramework
                      ? <><span className="mr-1">{FRAMEWORK_LOGOS[selectedFramework]}</span><span className="font-weight-medium capitalize">{selectedFramework}</span></>
                      : currentContainerConfig?.framework
                        ? <><span className="mr-1">{FRAMEWORK_LOGOS[currentContainerConfig.framework]}</span><span className="font-weight-medium capitalize">{currentContainerConfig.framework}</span></>
                        : `${containerType === 'backend' ? 'Backend' : 'Frontend'} Framework Presets`}
                    <ChevronDown size={12} className={`transition-transform ${showFrameworkDropdown ? 'rotate-180' : ''}`} />
                  </button>
                  {showFrameworkDropdown && (
                    <div className="absolute right-0 mt-1 w-56 bg-white shadow-md rounded-md border border-gray-200 z-10">
                      <div className="py-1 px-2 border-b border-gray-100 typography-caption text-gray-500">
                        Select {containerType} framework command
                      </div>
                      <ul className="py-1 max-h-60 overflow-y-auto">
                        {getFrameworksForType().map(([key, {command, description}]) => (
                          <li key={key}>
                            <button
                              onClick={() => {
                                setCommand(command);
                                setSelectedFramework(key);
                                setShowFrameworkDropdown(false);
                              }}
                              className={`w-full text-left px-3 py-2 typography-body-sm hover:bg-gray-50 flex items-center gap-2 ${selectedFramework === key ? 'bg-orange-50 text-orange-600' : ''}`}
                            >
                              <span className="flex-shrink-0">{FRAMEWORK_LOGOS[key]}</span>
                              <span className="font-weight-medium capitalize">{key}</span>
                              <span className="typography-caption text-gray-500 ml-auto">{description}</span>
                              {selectedFramework === key && (
                                <Check size={14} className="text-orange-500 ml-1 flex-shrink-0" />
                              )}
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                {/* Start Command */}
                <div className="flex items-center gap-2 mb-2">
                  {selectedFramework ? (
                    <span className="flex-shrink-0">{FRAMEWORK_LOGOS[selectedFramework]}</span>
                  ) : currentContainerConfig?.framework ? (
                    <span className="flex-shrink-0">{FRAMEWORK_LOGOS[currentContainerConfig.framework] || <Terminal className="h-4 w-4 text-gray-500" />}</span>
                  ) : (
                    <Terminal className="h-4 w-4 text-gray-500" />
                  )}
                  <input
                    type="text"
                    value={command}
                    onChange={(e) => setCommand(e.target.value)}
                    placeholder={containerType === 'backend' ? 'uvicorn main:app --host 0.0.0.0 --port 8000' : 'npm run build'}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm"
                  />
                </div>

                {/* Build Command - Only for Backend */}
                {containerType === 'backend' && (
                  <div className="space-y-2">
                    <h3 className="font-weight-medium text-[13px] text-gray-700">Build Command</h3>
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-gray-500" />
                      <input
                        type="text"
                        value={buildCommand}
                        onChange={(e) => setBuildCommand(e.target.value)}
                        placeholder="pip install -r requirements.txt"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm"
                      />
                    </div>
                  </div>
                )}

                {/* Package Installation - Only for Frontend */}
                {containerType === 'frontend' && (
                  <div className="p-3 bg-gray-50 rounded-md">
                    <label className="flex items-start gap-2 cursor-pointer">
                      <div className="pt-0.5">
                        <input
                          type="checkbox"
                          checked={installPackages}
                          onChange={(e) => setInstallPackages(e.target.checked)}
                          className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
                        />
                      </div>
                      <div>
                        <div className="flex items-center gap-1.5 typography-body-sm font-weight-medium text-gray-700">
                          <Package className="h-4 w-4 text-gray-500" />
                          Install packages first
                        </div>
                        <p className="typography-caption text-gray-500 mt-1">
                          Run {getPackageInstallCommand()} before building your application
                        </p>
                      </div>
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* Preview Command */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">Command Preview</h2>
              </div>
              <div className="bg-gray-800 text-green-400 typography-body-sm p-3 rounded-md overflow-x-auto whitespace-pre-wrap">
                {containerType === 'backend' ? (
                  <>
                    <span className="text-blue-300"># Build Command:</span><br/>
                    <span className="text-yellow-300">$ {buildCommand}</span><br/><br/>
                    <span className="text-blue-300"># Start Command:</span><br/>
                    <span className="text-yellow-300">$ {command}</span>
                  </>
                ) : (
                  <>
                    {envVariables.length > 0 && envVariables.some((v: EnvVariable) => v.key.trim() !== '') ? (
                      <>
                        <span className="text-yellow-300">$ {getEnvString()}</span> {fullCommand}
                      </>
                    ) : (
                      <>$ {fullCommand}</>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Save Configuration */}
            <div className="mb-6">
              <button
                onClick={saveCurrentConfig}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors"
              >
                <Save size={14} className="text-gray-700" />
                Save Configuration
              </button>
              <p className="typography-caption text-gray-500 mt-1 text-center">
                Save this configuration for future deployments
              </p>
            </div>
          </div>

          {/* Launch Button - Update text based on container type */}
          <div className="absolute bottom-0 left-0 right-0 pt-4 pb-5 px-[20px] border-t bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]">
            {deploymentError && (
              <div className="mb-3 p-2 bg-red-50 border border-red-300 rounded-md">
                <p className="typography-body-sm text-red-600">{deploymentError}</p>
              </div>
            )}
            <button
              onClick={handleLaunch}
              disabled={isDeploying || showLaunchAnimation}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 typography-body-sm font-weight-medium text-white bg-orange-500 hover:bg-orange-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isDeploying || showLaunchAnimation ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {showLaunchAnimation ? 'Launching...' : 'Deploying...'}
                </>
              ) : (
                <>
                  <Rocket className="h-4 w-4" />
                  {selectedDeployment ? `Update ${containerType === 'backend' ? 'Backend' : 'Frontend'} Deployment` : `Create New ${containerType === 'backend' ? 'Backend' : 'Frontend'} App`}
                </>
              )}
            </button>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default DeploymentInterface;